/* Quill Editor Styles */
@import 'quill/dist/quill.snow.css';

/* Custom Quill styling to match the admin form design */
.ql-toolbar {
  border-top: 1px solid #d1d5db !important;
  border-left: 1px solid #d1d5db !important;
  border-right: 1px solid #d1d5db !important;
  border-bottom: none !important;
  background-color: #f9fafb;
  border-radius: 0.375rem 0.375rem 0 0;
  padding: 8px 12px;
}

.ql-container {
  border-bottom: 1px solid #d1d5db !important;
  border-left: 1px solid #d1d5db !important;
  border-right: 1px solid #d1d5db !important;
  border-top: none !important;
  border-radius: 0 0 0.375rem 0.375rem;
  font-family: inherit;
}

.ql-editor {
  min-height: 120px;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
  padding: 12px 16px;
}

.ql-editor.ql-blank::before {
  color: #9ca3af;
  font-style: italic;
}

/* Toolbar button styling */
.ql-toolbar .ql-formats {
  margin-right: 12px;
}

.ql-toolbar button {
  padding: 4px;
  margin: 0 1px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.ql-toolbar button:hover {
  background-color: #e5e7eb;
}

.ql-toolbar button.ql-active {
  background-color: #f97316;
  color: white;
}

.ql-toolbar .ql-picker {
  color: #374151;
}

.ql-toolbar .ql-picker-options {
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.ql-toolbar .ql-picker-item:hover {
  background-color: #f3f4f6;
}

.ql-toolbar .ql-picker-item.ql-selected {
  background-color: #f97316;
  color: white;
}

/* Focus styles */
.ql-container.ql-snow.ql-focused {
  border-color: #f97316;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

.ql-toolbar.ql-snow.ql-focused {
  border-color: #f97316;
}

/* Content styling in editor */
.ql-editor h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.ql-editor h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.ql-editor h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.ql-editor p {
  margin-bottom: 0.75rem;
  line-height: 1.6;
}

.ql-editor ul, .ql-editor ol {
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
}

.ql-editor li {
  margin-bottom: 0.25rem;
}

.ql-editor a {
  color: #f97316;
  text-decoration: underline;
}

.ql-editor a:hover {
  color: #ea580c;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .ql-toolbar {
    padding: 6px 8px;
  }

  .ql-toolbar .ql-formats {
    margin-right: 8px;
  }

  .ql-editor {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* Preview styles to match the editor content */
.prose h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.prose h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.prose h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.prose p {
  margin-bottom: 0.75rem;
  line-height: 1.6;
  color: #374151;
}

.prose ul, .prose ol {
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.25rem;
  color: #374151;
}

.prose a {
  color: #f97316;
  text-decoration: underline;
}

.prose a:hover {
  color: #ea580c;
}

.prose strong {
  font-weight: 600;
  color: #1f2937;
}

.prose em {
  font-style: italic;
}

.prose u {
  text-decoration: underline;
}

/* Product page description styles */
.prose.prose-sm {
  font-size: 0.875rem;
  line-height: 1.5;
}

.prose.prose-sm h1 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.prose.prose-sm h2 {
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
}

.prose.prose-sm h3 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.prose.prose-sm p {
  margin-bottom: 0.5rem;
}

.prose.prose-sm ul, .prose.prose-sm ol {
  margin-bottom: 0.5rem;
  padding-left: 1.25rem;
}

.prose.prose-sm li {
  margin-bottom: 0.125rem;
}

/* Ensure proper spacing and alignment */
.prose.max-w-none {
  max-width: none;
}

/* Remove default margins for first and last elements */
.prose > *:first-child {
  margin-top: 0;
}

.prose > *:last-child {
  margin-bottom: 0;
}

/* Custom Rich Text Editor Styles */
[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

[contenteditable="true"]:focus {
  outline: none;
  border-color: #f97316;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

/* Custom toolbar styles */
.rich-text-toolbar {
  background-color: #f9fafb;
  border-bottom: 1px solid #d1d5db;
  padding: 8px 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.rich-text-toolbar button {
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: white;
  color: #374151;
  transition: all 0.2s;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rich-text-toolbar button:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.rich-text-toolbar button.active {
  background-color: #f97316;
  border-color: #f97316;
  color: white;
}

.rich-text-toolbar .separator {
  width: 1px;
  background-color: #d1d5db;
  margin: 0 4px;
}

/* Content editable area styles */
.rich-text-content {
  padding: 16px;
  min-height: 150px;
  background-color: white;
  line-height: 1.6;
  font-size: 14px;
  color: #374151;
}

.rich-text-content:focus {
  outline: none;
}

/* Ensure proper formatting in contentEditable */
.rich-text-content h1,
.rich-text-content h2,
.rich-text-content h3 {
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 0.5rem;
}

.rich-text-content h1 {
  font-size: 1.25rem;
}

.rich-text-content h2 {
  font-size: 1.125rem;
}

.rich-text-content h3 {
  font-size: 1rem;
}

.rich-text-content p {
  margin-bottom: 0.5rem;
}

.rich-text-content ul,
.rich-text-content ol {
  margin-bottom: 0.5rem;
  padding-left: 1.25rem;
}

.rich-text-content li {
  margin-bottom: 0.125rem;
}

.rich-text-content strong {
  font-weight: 600;
}

.rich-text-content em {
  font-style: italic;
}

.rich-text-content u {
  text-decoration: underline;
}

/* Enhanced table styles for both editor and display */
.prose table,
.rich-text-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.prose th,
.prose td,
.rich-text-content th,
.rich-text-content td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.prose th,
.rich-text-content th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.prose td,
.rich-text-content td {
  color: #6b7280;
  font-size: 0.875rem;
}

.prose tbody tr:hover,
.rich-text-content tbody tr:hover {
  background-color: #f9fafb;
}

.prose tbody tr:nth-child(even),
.rich-text-content tbody tr:nth-child(even) {
  background-color: #fafafa;
}

/* Responsive table styles */
@media (max-width: 768px) {
  .prose table,
  .rich-text-content table {
    min-width: 500px;
    font-size: 0.8rem;
  }

  .prose th,
  .prose td,
  .rich-text-content th,
  .rich-text-content td {
    padding: 8px 6px;
    white-space: nowrap;
  }

  .prose th,
  .rich-text-content th {
    font-size: 0.75rem;
  }

  .prose td,
  .rich-text-content td {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .prose table,
  .rich-text-content table {
    min-width: 400px;
    font-size: 0.75rem;
  }

  .prose th,
  .prose td,
  .rich-text-content th,
  .rich-text-content td {
    padding: 6px 4px;
  }

  .prose th,
  .rich-text-content th {
    font-size: 0.7rem;
  }

  .prose td,
  .rich-text-content td {
    font-size: 0.7rem;
  }
}
