import React, { useState, useEffect } from "react";

export default function ManageAddresses(props) {
  console.log("🚀 ~ ManageAddresses ~ props:", props);
  const { showNewForm } = false;
  const [addresses, setAddresses] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState(null);

  // Form state
  const [addressForm, setAddressForm] = useState({
    fullName: "",
    phone: "",
    address: "",
    city: "",
    district: "",
    nearest_busstand: "",
    school_name: "",
    whatsapp_number: "",
    zipCode: "",
    instructions: "",
    isDefault: false,
  });

  // Form errors
  const [formErrors, setFormErrors] = useState({});

  // Load addresses on component mount
  useEffect(() => {
    fetchAddresses();

    // Listen for external event to open the address form
    const handleOpenAddressForm = () => {
      setShowForm(true);
    };

    document.addEventListener("openAddressForm", handleOpenAddressForm);

    // Clean up the event listener when the component unmounts
    return () => {
      document.removeEventListener("openAddressForm", handleOpenAddressForm);
    };
  }, []);

  // Reset form when showForm changes
  useEffect(() => {
    if (!showForm) {
      setEditingAddress(null);
      resetForm();
    }
  }, [showForm]);

  // Fetch addresses from API
  const fetchAddresses = async () => {
    setIsLoading(true);
    try {
      if (typeof window !== "undefined" && window.ApiClient) {
        const data = await window.ApiClient.getAddresses();
        if (data && data.addresses) {
          setAddresses(data.addresses);
        }
      }
    } catch (error) {
      console.error("Error fetching addresses:", error);
      showToast("Failed to load addresses", "error");

      // If authentication error, redirect to login
      if (error.message === "Authentication required") {
        const currentPath = window.location.pathname;
        window.location.href = `/login?redirect=${encodeURIComponent(
          currentPath
        )}`;
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handler for form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const inputValue = type === "checkbox" ? checked : value;

    setAddressForm({
      ...addressForm,
      [name]: inputValue,
    });

    // Clear error when field is modified
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: "",
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!addressForm.fullName?.trim()) errors.fullName = "Name is required";
    if (!addressForm.phone?.trim()) errors.phone = "Phone is required";
    if (!addressForm.address?.trim()) errors.address = "Address is required";
    if (!addressForm.city?.trim()) errors.city = "City is required";
    if (!addressForm.district?.trim()) errors.district = "District is required";
    if (!addressForm.nearest_busstand?.trim()) errors.nearest_busstand = "Nearest bus stand is required";
    if (!addressForm.school_name?.trim()) errors.school_name = "School name is required";
    if (!addressForm.whatsapp_number?.trim()) errors.whatsapp_number = "WhatsApp number is required";
    if (!addressForm.zipCode?.trim()) errors.zipCode = "PIN Code is required";

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Reset form
  const resetForm = () => {
    setAddressForm({
      fullName: "",
      phone: "",
      address: "",
      city: "",
      district: "",
      nearest_busstand: "",
      school_name: "",
      whatsapp_number: "",
      zipCode: "",
      instructions: "",
      isDefault: false,
    });
    setFormErrors({});
  };

  // Handle saving an address (create or update)
  const handleSaveAddress = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const addressData = {
        full_name: addressForm.fullName,
        phone: addressForm.phone,
        address: addressForm.address,
        city: addressForm.city,
        district: addressForm.district,
        nearest_busstand: addressForm.nearest_busstand,
        school_name: addressForm.school_name,
        whatsapp_number: addressForm.whatsapp_number,
        zip_code: addressForm.zipCode,
        instructions: addressForm.instructions || "",
        is_default: addressForm.isDefault,
      };

      if (typeof window !== "undefined" && window.ApiClient) {
        let result;

        if (editingAddress) {
          // Update existing address
          result = await window.ApiClient.updateAddress(
            editingAddress.id,
            addressData
          );

          if (result && result.address) {
            showToast("Address updated successfully");
          } else {
            throw new Error("Failed to update address");
          }
        } else {
          // Add new address
          result = await window.ApiClient.addAddress(addressData);

          if (result && result.address) {
            showToast("Address added successfully");
          } else {
            throw new Error("Failed to add address");
          }
        }

        // Refresh the addresses list
        await fetchAddresses();
        // Hide the form
        setShowForm(false);
      }
    } catch (error) {
      console.error("Error saving address:", error);
      showToast("Failed to save address", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle setting an address as default
  const handleSetDefault = async (addressId) => {
    try {
      // Find the address
      const address = addresses.find((addr) => addr.id === addressId);
      if (!address) return;

      // Create a copy with is_default set to true
      const updatedAddress = {
        full_name: address.full_name,
        phone: address.phone,
        address: address.address,
        city: address.city,
        district: address.district,
        nearest_busstand: address.nearest_busstand,
        school_name: address.school_name,
        whatsapp_number: address.whatsapp_number,
        zip_code: address.zip_code,
        instructions: address.instructions || "",
        is_default: true,
      };

      if (typeof window !== "undefined" && window.ApiClient) {
        const result = await window.ApiClient.updateAddress(
          addressId,
          updatedAddress
        );

        if (result && result.address) {
          showToast("Default address updated");
          await fetchAddresses();
        } else {
          throw new Error("Failed to update address");
        }
      }
    } catch (error) {
      console.error("Error setting default address:", error);
      showToast("Failed to update default address", "error");
    }
  };

  // Handle editing an address
  const handleEdit = (address) => {
    setEditingAddress(address);
    setAddressForm({
      fullName: address.full_name,
      phone: address.phone,
      address: address.address,
      city: address.city,
      district: address.district,
      nearest_busstand: address.nearest_busstand,
      school_name: address.school_name,
      whatsapp_number: address.whatsapp_number,
      zipCode: address.zip_code,
      instructions: address.instructions || "",
      isDefault: address.is_default,
    });
    setShowForm(true);
    window.scrollTo(0, 0);
  };

  // Show delete confirmation
  const handleShowDeleteConfirm = (addressId) => {
    setConfirmDelete(addressId);
  };

  // Cancel delete
  const handleCancelDelete = () => {
    setConfirmDelete(null);
  };

  // Handle deleting an address
  const handleConfirmDelete = async () => {
    if (!confirmDelete) return;

    try {
      if (typeof window !== "undefined" && window.ApiClient) {
        const result = await window.ApiClient.deleteAddress(confirmDelete);

        if (result && result.success) {
          showToast("Address deleted successfully");
          // Refresh the addresses list
          await fetchAddresses();
        } else {
          throw new Error("Failed to delete address");
        }
      }
    } catch (error) {
      console.error("Error deleting address:", error);
      showToast("Failed to delete address", "error");
    } finally {
      setConfirmDelete(null);
    }
  };

  // Show a toast notification
  const showToast = (message, type = "success") => {
    const toast = document.createElement("div");
    toast.className =
      "fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-lg z-50 flex items-center opacity-0 transition-opacity duration-300";

    const icon = document.createElement("span");
    icon.className = "material-icons-round mr-2";

    if (type === "error") {
      icon.textContent = "error";
    } else if (type === "success") {
      icon.textContent = "check_circle";
    } else {
      icon.textContent = "info";
    }

    toast.appendChild(icon);

    const text = document.createElement("span");
    text.textContent = message;
    toast.appendChild(text);

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.style.opacity = "1";
    }, 10);

    setTimeout(() => {
      toast.style.opacity = "0";
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  };

  return (
    <div className="max-w-xl mx-auto px-4 py-5">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-5">
        <h1 className="text-xl font-semibold text-gray-800">
          {showForm
            ? editingAddress
              ? "Edit Address"
              : "Add New Address"
            : "Your Addresses"}
        </h1>

        {!showForm && (
          <button
            onClick={() => setShowForm(true)}
            className="bg-[#FF6B35] text-white px-3 py-2 rounded-lg flex items-center hover:bg-[#e55c28] transition-colors"
          >
            <span className="material-icons-round text-sm mr-1">add</span>
            <span>Add New</span>
          </button>
        )}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center my-12">
          <div className="w-10 h-10 border-3 border-gray-200 border-t-[#FF6B35] rounded-full animate-spin"></div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {confirmDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl p-5 max-w-sm w-full">
            <h2 className="text-lg font-semibold text-gray-800 mb-3">
              Delete Address
            </h2>
            <p className="text-gray-600 mb-4">
              Are you sure you want to delete this address? This action cannot
              be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={handleCancelDelete}
                className="flex-1 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                className="flex-1 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Address Form */}
      {showForm && (
        <div className="bg-white rounded-xl shadow-sm p-5 mb-6">
          <form onSubmit={handleSaveAddress}>
            {/* Full Name */}
            <div className="mb-4">
              <label
                htmlFor="fullName"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Full Name
              </label>
              <div
                className={`relative rounded-lg overflow-hidden transition-all ${
                  formErrors.fullName ? "ring-1 ring-red-500" : ""
                }`}
              >
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <span className="material-icons-round text-gray-400">
                    person
                  </span>
                </div>
                <input
                  type="text"
                  id="fullName"
                  name="fullName"
                  value={addressForm.fullName}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35]"
                  placeholder="Enter your full name"
                />
              </div>
              {formErrors.fullName && (
                <p className="mt-1 text-red-600 text-xs flex items-center">
                  <span className="material-icons-round text-xs mr-1">
                    error
                  </span>
                  {formErrors.fullName}
                </p>
              )}
            </div>

            {/* Phone */}
            <div className="mb-4">
              <label
                htmlFor="phone"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Phone Number
              </label>
              <div
                className={`relative rounded-lg overflow-hidden transition-all ${
                  formErrors.phone ? "ring-1 ring-red-500" : ""
                }`}
              >
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <span className="material-icons-round text-gray-400">
                    phone
                  </span>
                </div>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={addressForm.phone}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35]"
                  placeholder="Enter your phone number"
                />
              </div>
              {formErrors.phone && (
                <p className="mt-1 text-red-600 text-xs flex items-center">
                  <span className="material-icons-round text-xs mr-1">
                    error
                  </span>
                  {formErrors.phone}
                </p>
              )}
            </div>

            {/* Address */}
            <div className="mb-4">
              <label
                htmlFor="address"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Address
              </label>
              <div
                className={`relative rounded-lg overflow-hidden transition-all ${
                  formErrors.address ? "ring-1 ring-red-500" : ""
                }`}
              >
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <span className="material-icons-round text-gray-400">
                    home
                  </span>
                </div>
                <textarea
                  id="address"
                  name="address"
                  value={addressForm.address}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35]"
                  placeholder="Enter your complete address"
                />
              </div>
              {formErrors.address && (
                <p className="mt-1 text-red-600 text-xs flex items-center">
                  <span className="material-icons-round text-xs mr-1">
                    error
                  </span>
                  {formErrors.address}
                </p>
              )}
            </div>

            {/* City and District */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label
                  htmlFor="city"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  City
                </label>
                <div
                  className={`relative rounded-lg overflow-hidden transition-all ${
                    formErrors.city ? "ring-1 ring-red-500" : ""
                  }`}
                >
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <span className="material-icons-round text-gray-400">
                      location_city
                    </span>
                  </div>
                  <input
                    type="text"
                    id="city"
                    name="city"
                    value={addressForm.city}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35]"
                    placeholder="Enter your city"
                  />
                </div>
                {formErrors.city && (
                  <p className="mt-1 text-red-600 text-xs flex items-center">
                    <span className="material-icons-round text-xs mr-1">
                      error
                    </span>
                    {formErrors.city}
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="district"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  District
                </label>
                <div
                  className={`relative rounded-lg overflow-hidden transition-all ${
                    formErrors.district ? "ring-1 ring-red-500" : ""
                  }`}
                >
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <span className="material-icons-round text-gray-400">
                      map
                    </span>
                  </div>
                  <input
                    type="text"
                    id="district"
                    name="district"
                    value={addressForm.district}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35]"
                    placeholder="Enter your district"
                  />
                </div>
                {formErrors.district && (
                  <p className="mt-1 text-red-600 text-xs flex items-center">
                    <span className="material-icons-round text-xs mr-1">
                      error
                    </span>
                    {formErrors.district}
                  </p>
                )}
              </div>
            </div>

            {/* Nearest Bus Stand */}
            <div className="mb-4">
              <label
                htmlFor="nearest_busstand"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Nearest Bus Stand
              </label>
              <div
                className={`relative rounded-lg overflow-hidden transition-all ${
                  formErrors.nearest_busstand ? "ring-1 ring-red-500" : ""
                }`}
              >
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <span className="material-icons-round text-gray-400">
                    directions_bus
                  </span>
                </div>
                <input
                  type="text"
                  id="nearest_busstand"
                  name="nearest_busstand"
                  value={addressForm.nearest_busstand}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35]"
                  placeholder="Enter nearest bus stand"
                />
              </div>
              {formErrors.nearest_busstand && (
                <p className="mt-1 text-red-600 text-xs flex items-center">
                  <span className="material-icons-round text-xs mr-1">
                    error
                  </span>
                  {formErrors.nearest_busstand}
                </p>
              )}
            </div>

            {/* School Name */}
            <div className="mb-4">
              <label
                htmlFor="school_name"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                School Name
              </label>
              <div
                className={`relative rounded-lg overflow-hidden transition-all ${
                  formErrors.school_name ? "ring-1 ring-red-500" : ""
                }`}
              >
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <span className="material-icons-round text-gray-400">
                    school
                  </span>
                </div>
                <input
                  type="text"
                  id="school_name"
                  name="school_name"
                  value={addressForm.school_name}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35]"
                  placeholder="Enter your school name"
                />
              </div>
              {formErrors.school_name && (
                <p className="mt-1 text-red-600 text-xs flex items-center">
                  <span className="material-icons-round text-xs mr-1">
                    error
                  </span>
                  {formErrors.school_name}
                </p>
              )}
            </div>

            {/* WhatsApp Number */}
            <div className="mb-4">
              <label
                htmlFor="whatsapp_number"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                WhatsApp Number
              </label>
              <div
                className={`relative rounded-lg overflow-hidden transition-all ${
                  formErrors.whatsapp_number ? "ring-1 ring-red-500" : ""
                }`}
              >
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <span className="material-icons-round text-gray-400">
                    whatsapp
                  </span>
                </div>
                <input
                  type="tel"
                  id="whatsapp_number"
                  name="whatsapp_number"
                  value={addressForm.whatsapp_number}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35]"
                  placeholder="Enter your WhatsApp number"
                />
              </div>
              {formErrors.whatsapp_number && (
                <p className="mt-1 text-red-600 text-xs flex items-center">
                  <span className="material-icons-round text-xs mr-1">
                    error
                  </span>
                  {formErrors.whatsapp_number}
                </p>
              )}
            </div>

            {/* ZIP Code */}
            <div className="mb-4">
              <label
                htmlFor="zipCode"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                ZIP Code
              </label>
              <div
                className={`relative rounded-lg overflow-hidden transition-all ${
                  formErrors.zipCode ? "ring-1 ring-red-500" : ""
                }`}
              >
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <span className="material-icons-round text-gray-400">
                    pin
                  </span>
                </div>
                <input
                  type="text"
                  id="zipCode"
                  name="zipCode"
                  value={addressForm.zipCode}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35]"
                  placeholder="Enter your PIN Code"
                />
              </div>
              {formErrors.zipCode && (
                <p className="mt-1 text-red-600 text-xs flex items-center">
                  <span className="material-icons-round text-xs mr-1">
                    error
                  </span>
                  {formErrors.zipCode}
                </p>
              )}
            </div>

            {/* Instructions */}
            <div className="mb-4">
              <label
                htmlFor="instructions"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Delivery Instructions (Optional)
              </label>
              <div className="relative rounded-lg overflow-hidden">
                <div className="absolute top-3 left-3 flex items-start pointer-events-none">
                  <span className="material-icons-round text-gray-400">
                    note
                  </span>
                </div>
                <textarea
                  id="instructions"
                  name="instructions"
                  value={addressForm.instructions}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35]"
                  placeholder="Any special instructions for delivery"
                />
              </div>
            </div>

            {/* Default Address Option */}
            <div className="flex items-center mb-6">
              <input
                type="checkbox"
                id="isDefault"
                name="isDefault"
                checked={addressForm.isDefault}
                onChange={handleInputChange}
                className="h-4 w-4 text-[#FF6B35] focus:ring-[#FF6B35] rounded"
              />
              <label
                htmlFor="isDefault"
                className="ml-2 text-sm text-gray-700"
              >
                Set as default address
              </label>
            </div>

            {/* Form Actions */}
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={() => setShowForm(false)}
                className="flex-1 py-3 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 py-3 bg-[#FF6B35] text-white rounded-lg font-medium hover:bg-[#e55c28] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Saving...
                  </>
                ) : (
                  "Save Address"
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Addresses List */}
      {!isLoading && !showForm && (
        <>
          {addresses.length === 0 ? (
            <div className="bg-white rounded-xl shadow-sm p-6 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto flex items-center justify-center mb-4">
                <span className="material-icons-round text-gray-400 text-2xl">
                  location_off
                </span>
              </div>
              <h2 className="text-lg font-medium text-gray-800 mb-1">
                No addresses found
              </h2>
              <p className="text-gray-500 mb-4">
                Add your first delivery address to get started
              </p>
              <button
                onClick={() => setShowForm(true)}
                className="bg-[#FF6B35] text-white px-4 py-2 rounded-lg hover:bg-[#e55c28] transition-colors inline-flex items-center"
              >
                <span className="material-icons-round mr-1">add</span>
                Add New Address
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {addresses.map((address) => (
                <div
                  key={address.id}
                  className={`bg-white rounded-xl shadow-sm p-4 ${
                    address.is_default ? "border-2 border-[#FF6B35]" : ""
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h3 className="font-medium text-gray-800">
                          {address.full_name}
                        </h3>
                        {address.is_default && (
                          <span className="ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded">
                            Default
                          </span>
                        )}
                      </div>
                      <p className="text-gray-500 text-sm mt-1">
                        {address.phone}
                      </p>
                      <p className="text-gray-600 text-sm mt-1">
                        {address.address}, {address.city}, {address.district} - {address.zip_code}
                      </p>
                      <p className="text-gray-600 text-sm mt-1">
                        Nearest Bus Stand: {address.nearest_busstand}
                      </p>
                      <p className="text-gray-600 text-sm mt-1">
                        School: {address.school_name}
                      </p>
                      <p className="text-gray-600 text-sm mt-1">
                        WhatsApp: {address.whatsapp_number}
                      </p>
                      {address.instructions && (
                        <p className="text-gray-500 text-xs mt-2 italic">
                          Note: {address.instructions}
                        </p>
                      )}
                    </div>
                    <div className="flex flex-col space-y-2">
                      <button
                        onClick={() => handleEdit(address)}
                        className="text-blue-600 p-1 hover:bg-blue-50 rounded transition-colors"
                        aria-label="Edit address"
                      >
                        <span className="material-icons-round">edit</span>
                      </button>
                      <button
                        onClick={() => handleShowDeleteConfirm(address.id)}
                        className="text-red-600 p-1 hover:bg-red-50 rounded transition-colors"
                        aria-label="Delete address"
                      >
                        <span className="material-icons-round">delete</span>
                      </button>
                    </div>
                  </div>
                  {!address.is_default && (
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <button
                        onClick={() => handleSetDefault(address.id)}
                        className="text-sm text-[#FF6B35] font-medium hover:text-[#e55c28] transition-colors flex items-center"
                      >
                        <span className="material-icons-round text-sm mr-1">
                          star
                        </span>
                        Set as default
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
}
