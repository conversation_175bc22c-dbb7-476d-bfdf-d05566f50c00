-- DROP TABLE IF EXISTS order_items;
-- DROP TABLE IF EXISTS payment_transactions;
-- DROP TABLE IF EXISTS orders;
-- DROP TABLE IF EXISTS coupon_usages;
-- DROP TABLE IF EXISTS delivery_boy_locations;
-- DROP TABLE IF EXISTS location_delivery_fees;
-- DROP TABLE IF EXISTS order_locations;
-- DROP TABLE IF EXISTS product_images;
-- DROP TABLE IF EXISTS product_tags;
-- DROP TABLE IF EXISTS products;
-- DROP TABLE IF EXISTS categories;
-- DROP TABLE IF EXISTS user_addresses;
-- DROP TABLE IF EXISTS otp_verifications;
-- DROP TABLE IF EXISTS coupons;
-- DROP TABLE IF EXISTS users;
-- DROP TABLE IF EXISTS promotions;
-- DROP TABLE IF EXISTS delivery_fee_settings;
-- DROP TABLE IF EXISTS payment_method_settings;

-- PRAGMA defer_foreign_keys=TRUE;
-- CREATE TABLE categories (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   name TEXT NOT NULL,
--   icon TEXT NOT NULL,
--   color TEXT NOT NULL
-- );
-- -- Educational Categories for Sreekar Publishers
-- INSERT INTO categories VALUES(1,'Mathematics','📐','#4F46E5');
-- INSERT INTO categories VALUES(2,'Science','🔬','#059669');
-- INSERT INTO categories VALUES(3,'Social Studies','🌍','#DC2626');
-- INSERT INTO categories VALUES(4,'Telugu Language','📚','#7C2D12');
-- INSERT INTO categories VALUES(5,'English Language','📖','#1D4ED8');
-- INSERT INTO categories VALUES(6,'Hindi Language','📝','#B45309');
-- INSERT INTO categories VALUES(7,'Grade 6 Books','🎒','#6366F1');
-- INSERT INTO categories VALUES(8,'Grade 7 Books','🎒','#8B5CF6');
-- INSERT INTO categories VALUES(9,'Grade 8 Books','🎒','#EC4899');
-- INSERT INTO categories VALUES(10,'Grade 9 Books','🎒','#F59E0B');
-- INSERT INTO categories VALUES(11,'Grade 10 Books','🎒','#10B981');
-- CREATE TABLE products (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   name TEXT NOT NULL,
--   url_slug TEXT NOT NULL UNIQUE,
--   description TEXT,
--   price DECIMAL(10, 2) NOT NULL,
--   old_price DECIMAL(10, 2),
--   image TEXT NOT NULL,
--   category_id INTEGER,
--   is_featured BOOLEAN DEFAULT 0,
--   is_new BOOLEAN DEFAULT 0,
--   is_on_sale BOOLEAN DEFAULT 0,
--   rating DECIMAL(3, 1),
--   reviews_count INTEGER DEFAULT 0,
--   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, unit_type TEXT DEFAULT 'quantity', unit_value DECIMAL(10, 2) DEFAULT 1, is_available BOOLEAN DEFAULT 1, stock_quantity INTEGER DEFAULT 0, demo_pdf_url TEXT,
--   FOREIGN KEY (category_id) REFERENCES categories(id)
-- );
-- -- Educational Books for Sreekar Publishers

-- -- Grade 6 Books
-- INSERT INTO products VALUES(1,'Mathematics Textbook - Grade 6 (Telugu Medium)','math-grade6-telugu','<h3>Comprehensive Mathematics Textbook</h3><p>This <strong>comprehensive mathematics textbook</strong> is specially designed for Grade 6 students in <em>Telugu medium</em>. Our expertly crafted content covers all essential topics as per the state curriculum.</p><h3>Key Topics Covered:</h3><ul><li>Arithmetic operations and number systems</li><li>Basic geometry and shapes</li><li>Introduction to algebra</li><li>Fractions and decimals</li><li>Measurement and data handling</li></ul><p>Perfect for building a strong foundation in mathematics with <u>clear explanations</u> and practical examples.</p>',250,300,'https://images.unsplash.com/photo-*************-180dd4864904?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',1,1,0,1,4.5,25,'2025-01-15T10:00:00.000Z','quantity',1,1,50,'https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view');

-- INSERT INTO products VALUES(2,'Science Textbook - Grade 6 (English Medium)','science-grade6-english','Complete science textbook for Grade 6 students in English medium. Includes physics, chemistry, and biology concepts with practical experiments.',280,320,'https://images.unsplash.com/photo-***********84-543bc11b234d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',2,1,0,1,4.3,18,'2025-01-15T10:00:00.000Z','quantity',1,1,45,'https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view');

-- INSERT INTO products VALUES(3,'Social Studies - Grade 6 (Telugu Medium)','social-grade6-telugu','Comprehensive social studies textbook covering history, geography, and civics for Grade 6 students in Telugu medium.',220,260,'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',3,0,0,1,4.2,22,'2025-01-15T10:00:00.000Z','quantity',1,1,40,NULL);

-- INSERT INTO products VALUES(4,'Telugu Language Textbook - Grade 6','telugu-grade6','Telugu language and literature textbook for Grade 6 students. Includes grammar, poetry, and prose.',200,240,'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',4,0,0,1,4.4,30,'2025-01-15T10:00:00.000Z','quantity',1,1,60,NULL);

-- INSERT INTO products VALUES(5,'English Language Textbook - Grade 6','english-grade6','English language textbook for Grade 6 students with grammar, vocabulary, and comprehension exercises.',230,270,'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',5,0,0,1,4.1,20,'2025-01-15T10:00:00.000Z','quantity',1,1,35,NULL);

-- -- Grade 7 Books
-- INSERT INTO products VALUES(6,'Mathematics Textbook - Grade 7 (Telugu Medium)','math-grade7-telugu','Advanced mathematics textbook for Grade 7 students in Telugu medium. Covers algebra, geometry, and data handling.',270,310,'https://images.unsplash.com/photo-*************-180dd4864904?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',1,0,0,1,4.6,28,'2025-01-15T10:00:00.000Z','quantity',1,1,42,NULL);

-- INSERT INTO products VALUES(7,'Science Textbook - Grade 7 (English Medium)','science-grade7-english','Comprehensive science textbook for Grade 7 students in English medium with advanced concepts and experiments.',300,340,'https://images.unsplash.com/photo-***********84-543bc11b234d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',2,0,1,1,4.4,24,'2025-01-15T10:00:00.000Z','quantity',1,1,38,NULL);

-- INSERT INTO products VALUES(8,'Social Studies - Grade 7 (Telugu Medium)','social-grade7-telugu','Social studies textbook for Grade 7 covering Indian history, world geography, and constitutional studies.',240,280,'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',3,0,0,1,4.3,19,'2025-01-15T10:00:00.000Z','quantity',1,1,33,NULL);

-- -- Grade 8 Books
-- INSERT INTO products VALUES(9,'Mathematics Textbook - Grade 8 (English Medium)','math-grade8-english','Mathematics textbook for Grade 8 students in English medium covering advanced algebra and geometry.',290,330,'https://images.unsplash.com/photo-*************-180dd4864904?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',1,1,0,1,4.5,32,'2025-01-15T10:00:00.000Z','quantity',1,1,48,NULL);

-- INSERT INTO products VALUES(10,'Science Textbook - Grade 8 (Telugu Medium)','science-grade8-telugu','Complete science textbook for Grade 8 students in Telugu medium with practical applications.',310,350,'https://images.unsplash.com/photo-***********84-543bc11b234d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',2,0,0,1,4.2,26,'2025-01-15T10:00:00.000Z','quantity',1,1,41,NULL);

-- INSERT INTO products VALUES(11,'Hindi Language Textbook - Grade 8','hindi-grade8','Hindi language textbook for Grade 8 students with literature and grammar.',210,250,'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',6,0,0,1,4.0,15,'2025-01-15T10:00:00.000Z','quantity',1,1,30,NULL);

-- -- Grade 9 Books
-- INSERT INTO products VALUES(12,'Mathematics Textbook - Grade 9 (Telugu Medium)','math-grade9-telugu','Advanced mathematics for Grade 9 students including coordinate geometry and statistics.',320,360,'https://images.unsplash.com/photo-*************-180dd4864904?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',1,1,0,1,4.7,35,'2025-01-15T10:00:00.000Z','quantity',1,1,55,NULL);

-- INSERT INTO products VALUES(13,'Science Textbook - Grade 9 (English Medium)','science-grade9-english','Comprehensive science textbook for Grade 9 covering physics, chemistry, and biology in detail.',340,380,'https://images.unsplash.com/photo-***********84-543bc11b234d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',2,1,0,1,4.6,29,'2025-01-15T10:00:00.000Z','quantity',1,1,47,NULL);

-- INSERT INTO products VALUES(14,'Social Studies - Grade 9 (Telugu Medium)','social-grade9-telugu','Social studies for Grade 9 covering modern Indian history and democratic politics.',260,300,'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',3,0,0,1,4.4,21,'2025-01-15T10:00:00.000Z','quantity',1,1,36,NULL);

-- -- Grade 10 Books
-- INSERT INTO products VALUES(15,'Mathematics Textbook - Grade 10 (English Medium)','math-grade10-english','<h2>Complete Board Exam Preparation</h2><p>This <strong>comprehensive mathematics textbook</strong> is specifically designed for Grade 10 students preparing for their board examinations in <em>English medium</em>.</p><h3>What Makes This Book Special:</h3><ul><li><strong>Solved Examples:</strong> Step-by-step solutions for complex problems</li><li><strong>Practice Questions:</strong> Extensive question bank with varying difficulty levels</li><li><strong>Board Exam Focus:</strong> Aligned with latest examination patterns</li><li><strong>Clear Explanations:</strong> Easy-to-understand concepts with diagrams</li></ul><h3>Key Topics:</h3><ol><li>Real Numbers and Polynomials</li><li>Linear Equations and Quadratic Equations</li><li>Coordinate Geometry</li><li>Trigonometry</li><li>Statistics and Probability</li></ol><p><u>Perfect for achieving excellent results</u> in your board examinations!</p>',350,390,'https://images.unsplash.com/photo-*************-180dd4864904?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',1,1,1,1,4.8,42,'2025-01-15T10:00:00.000Z','quantity',1,1,65,'https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view');

-- INSERT INTO products VALUES(16,'Science Textbook - Grade 10 (Telugu Medium)','science-grade10-telugu','Board exam focused science textbook for Grade 10 students in Telugu medium.',370,410,'https://images.unsplash.com/photo-***********84-543bc11b234d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',2,1,1,1,4.7,38,'2025-01-15T10:00:00.000Z','quantity',1,1,58,NULL);

-- INSERT INTO products VALUES(17,'Social Studies - Grade 10 (English Medium)','social-grade10-english','Board exam preparation book for social studies covering economics and political science.',280,320,'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',3,0,1,1,4.5,27,'2025-01-15T10:00:00.000Z','quantity',1,1,44,NULL);

-- INSERT INTO products VALUES(18,'Telugu Language - Grade 10','telugu-grade10','Advanced Telugu language and literature for Grade 10 board exam preparation.',240,280,'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',4,0,0,1,4.6,33,'2025-01-15T10:00:00.000Z','quantity',1,1,52,NULL);

-- INSERT INTO products VALUES(19,'English Language - Grade 10','english-grade10','English language textbook for Grade 10 board exam with literature and grammar.',260,300,'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',5,0,0,1,4.4,31,'2025-01-15T10:00:00.000Z','quantity',1,1,49,NULL);

-- INSERT INTO products VALUES(20,'Hindi Language - Grade 10','hindi-grade10','Hindi language textbook for Grade 10 board exam preparation with poetry and prose.',230,270,'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',6,0,0,1,4.3,23,'2025-01-15T10:00:00.000Z','quantity',1,1,37,NULL);
-- CREATE TABLE product_images (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   product_id INTEGER NOT NULL,
--   image_url TEXT NOT NULL,
--   sort_order INTEGER DEFAULT 0,
--   FOREIGN KEY (product_id) REFERENCES products(id)
-- );
-- CREATE TABLE product_tags (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   product_id INTEGER NOT NULL,
--   tag TEXT NOT NULL,
--   FOREIGN KEY (product_id) REFERENCES products(id)
-- );

-- CREATE TABLE promotions (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   title TEXT NOT NULL,
--   description TEXT,
--   image TEXT NOT NULL,
--   url TEXT NOT NULL,
--   color TEXT NOT NULL,
--   active BOOLEAN DEFAULT 1
-- );
-- -- Educational Promotions for Sreekar Publishers
-- INSERT INTO promotions VALUES(1,'New Academic Year Sale','20% OFF on all Grade 10 Board Exam Books','https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80','/promo/board-exam-sale','linear-gradient(45deg, #4F46E5, #7C3AED)',1);
-- INSERT INTO promotions VALUES(2,'Complete Set Discount','Buy complete grade set and save ₹100','https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80','/promo/complete-set','linear-gradient(45deg, #059669, #10B981)',1);
-- INSERT INTO promotions VALUES(3,'Free Study Materials','Get free practice worksheets with every purchase','https://images.unsplash.com/photo-*************-180dd4864904?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80','/promo/free-materials','linear-gradient(45deg, #DC2626, #F59E0B)',1);
-- CREATE TABLE users (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   name TEXT NOT NULL,
--   phone_number TEXT UNIQUE NOT NULL,
--   points INTEGER DEFAULT 0,
--   level TEXT DEFAULT 'Bronze'
-- , is_verified BOOLEAN DEFAULT 0, email TEXT, created_at TIMESTAMP, role TEXT DEFAULT 'customer');
-- INSERT INTO users VALUES(1,'Alex','<EMAIL>',350,'Gold',0,NULL,'2025-05-13 16:23:30','customer');
-- INSERT INTO users VALUES(2,'Sam','<EMAIL>',120,'Silver',0,NULL,'2025-05-13 16:23:30','customer');
-- INSERT INTO users VALUES(3,'Jordan','<EMAIL>',520,'Platinum',0,NULL,'2025-05-13 16:23:30','customer');
-- INSERT INTO users VALUES(4,'Nookesh','07396019228',0,'Bronze',1,'<EMAIL>','2025-05-13 16:23:30','customer');
-- INSERT INTO users VALUES(5,'User6825','917396019228',0,'Bronze',1,NULL,'2025-05-13 16:23:30','customer');
-- INSERT INTO users VALUES(6,'Nookesh','+917396019228',0,'Bronze',1,'<EMAIL>','2025-05-13 16:23:30','delivery_boy');
-- INSERT INTO users VALUES(7,'User2527','+919866707382',0,'Bronze',1,NULL,'2025-05-13 16:23:30','customer');
-- CREATE TABLE user_addresses (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   user_id INTEGER NOT NULL,
--   full_name TEXT NOT NULL,
--   phone TEXT NOT NULL,
--   address TEXT NOT NULL,
--   city TEXT NOT NULL,
--   district TEXT NOT NULL,
--   nearest_busstand TEXT,
--   school_name TEXT,
--   whatsapp_number TEXT,
--   zip_code TEXT NOT NULL,
--   instructions TEXT,
--   is_default BOOLEAN DEFAULT 0,
--   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--   FOREIGN KEY (user_id) REFERENCES users(id)
-- );
-- INSERT INTO user_addresses VALUES(1,1,'Alex Johnson','************','123 Main Street','New York','New York','Grand Central Terminal','Central High School','******-344-4500','10001','Leave at the front door',1,'2025-05-02 03:06:11');
-- INSERT INTO user_addresses VALUES(2,1,'Alex Johnson','************','456 Park Avenue','New York','New York','Grand Central Terminal','Central High School','******-344-4500','10002','Ring bell twice',0,'2025-05-02 03:06:11');
-- INSERT INTO user_addresses VALUES(3,2,'Sam Smith','************','789 Broadway','Los Angeles','Los Angeles','Union Station','Union High School','******-922-4500','90001','',1,'2025-05-02 03:06:11');
-- INSERT INTO user_addresses VALUES(4,1,'NOOKESH Karri','07396019228','DNO 17-5-92,NEAR PEDA RAMALAYAM,ANAKAPALLI ,SANTHABAYALA,GAVARAPALEM','anakapalle','anakapalle','anakapalle','anakapalle','anakapalle','531001','',0,'2025-05-02 03:14:29');
-- INSERT INTO user_addresses VALUES(10,4,'NOOKESH Karri','07396019228','DNO 17-5-92,NEAR PEDA RAMALAYAM,ANAKAPALLI ,SANTHABAYALA,GAVARAPALEM','anakapalle','anakapalle','anakapalle','anakapalle','anakapalle','531001','',1,'2025-05-02 04:07:44');
-- INSERT INTO user_addresses VALUES(11,6,'NOOKESH Karri','07396019228','DNO 17-5-92,NEAR PEDA RAMALAYAM,ANAKAPALLI ,SANTHABAYALA,GAVARAPALEM','anakapalle','anakapalle','anakapalle','anakapalle','anakapalle','531001','',0,'2025-05-02 05:03:09');
-- INSERT INTO user_addresses VALUES(12,6,'NOOKESH Karri','07396019228','DNO 17-5-92,NEAR PEDA RAMALAYAM,ANAKAPALLI ,SANTHABAYALA,GAVARAPALEM','anakapalle','anakapalle','anakapalle','anakapalle','anakapalle','531001','',0,'2025-05-02 12:22:05');
-- CREATE TABLE otp_verifications (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   phone_number TEXT NOT NULL,
--   otp_code TEXT NOT NULL,
--   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--   expires_at TIMESTAMP NOT NULL,
--   is_used BOOLEAN DEFAULT 0,
--   attempts INTEGER DEFAULT 0
-- );
-- INSERT INTO otp_verifications VALUES(1,'07396019228','572286','2025-05-02 03:25:07','2025-05-02T03:35:07.888Z',1,0);
-- INSERT INTO otp_verifications VALUES(2,'07396019228','201594','2025-05-02 03:25:48','2025-05-02T03:35:48.257Z',1,0);
-- INSERT INTO otp_verifications VALUES(3,'07396019228','987249','2025-05-02 03:28:27','2025-05-02T03:38:27.561Z',1,0);
-- INSERT INTO otp_verifications VALUES(4,'917396019228','700022','2025-05-02 03:29:27','2025-05-02T03:39:27.420Z',1,0);
-- INSERT INTO otp_verifications VALUES(5,'917396019228','132924','2025-05-02 03:30:08','2025-05-02T03:40:08.703Z',1,0);
-- INSERT INTO otp_verifications VALUES(6,'07396019228','237875','2025-05-02 03:32:20','2025-05-02T03:42:20.908Z',1,0);
-- INSERT INTO otp_verifications VALUES(7,'07396019228','944052','2025-05-02 03:36:56','2025-05-02T03:46:56.518Z',1,0);
-- INSERT INTO otp_verifications VALUES(8,'07396019228','255295','2025-05-02 03:40:37','2025-05-02T03:50:37.569Z',1,0);
-- INSERT INTO otp_verifications VALUES(9,'07396019228','105630','2025-05-02 03:42:51','2025-05-02T03:52:51.488Z',1,0);
-- INSERT INTO otp_verifications VALUES(10,'07396019228','537113','2025-05-02 03:45:22','2025-05-02T03:55:22.330Z',1,0);
-- INSERT INTO otp_verifications VALUES(11,'07396019228','184689','2025-05-02 03:45:58','2025-05-02T03:55:58.950Z',1,0);
-- INSERT INTO otp_verifications VALUES(12,'07396019228','226926','2025-05-02 03:50:00','2025-05-02T04:00:00.558Z',1,0);
-- INSERT INTO otp_verifications VALUES(13,'917396019228','365104','2025-05-02 03:51:02','2025-05-02T04:01:02.370Z',1,0);
-- INSERT INTO otp_verifications VALUES(14,'07396019228','669647','2025-05-02 03:53:16','2025-05-02T04:03:16.482Z',1,0);
-- INSERT INTO otp_verifications VALUES(15,'07396019228','405234','2025-05-02 04:56:17','2025-05-02T05:06:17.402Z',1,0);
-- INSERT INTO otp_verifications VALUES(16,'+917396019228','249830','2025-05-02 05:02:52','2025-05-02T05:12:52.695Z',1,0);
-- INSERT INTO otp_verifications VALUES(17,'+917396019228','619105','2025-05-02 05:04:17','2025-05-02T05:14:17.595Z',1,0);
-- INSERT INTO otp_verifications VALUES(18,'+917396019228','617460','2025-05-02 11:47:13','2025-05-02T11:57:13.756Z',1,0);
-- INSERT INTO otp_verifications VALUES(19,'+917396019228','969375','2025-05-03 04:50:42','2025-05-03T05:00:42.212Z',1,0);
-- INSERT INTO otp_verifications VALUES(20,'+919866707382','353772','2025-05-03 05:08:59','2025-05-03T05:18:59.356Z',1,0);
-- INSERT INTO otp_verifications VALUES(21,'+917396019228','306140','2025-05-03 05:21:29','2025-05-03T05:31:29.967Z',1,0);
-- INSERT INTO otp_verifications VALUES(22,'+917396019228','402412','2025-05-03 05:22:11','2025-05-03T05:32:11.294Z',1,0);
-- INSERT INTO otp_verifications VALUES(23,'+917396019228','361308','2025-05-03 05:29:27','2025-05-03T05:39:27.968Z',1,0);
-- INSERT INTO otp_verifications VALUES(24,'+917396019228','714123','2025-05-03 05:37:14','2025-05-03T05:47:14.075Z',1,0);
-- INSERT INTO otp_verifications VALUES(25,'+917396019228','693048','2025-05-03 05:38:07','2025-05-03T05:48:07.575Z',1,0);
-- INSERT INTO otp_verifications VALUES(26,'+917396019228','242603','2025-05-03 08:54:23','2025-05-03T09:04:23.768Z',1,0);
-- INSERT INTO otp_verifications VALUES(27,'+917396019228','973368','2025-05-03 09:05:09','2025-05-03T09:15:09.118Z',1,0);
-- INSERT INTO otp_verifications VALUES(28,'+917396019228','924594','2025-05-03 09:05:37','2025-05-03T09:15:37.589Z',1,0);
-- INSERT INTO otp_verifications VALUES(29,'+917396019228','551626','2025-05-03 09:09:24','2025-05-03T09:19:24.362Z',1,0);
-- INSERT INTO otp_verifications VALUES(30,'+917396019228','213422','2025-05-03 10:22:34','2025-05-03T10:32:34.008Z',1,0);
-- INSERT INTO otp_verifications VALUES(31,'+917396019228','686260','2025-05-03 15:54:10','2025-05-03T16:04:10.071Z',1,0);
-- INSERT INTO otp_verifications VALUES(32,'+917396019228','774872','2025-05-05 02:21:25','2025-05-05T02:31:25.365Z',1,0);
-- INSERT INTO otp_verifications VALUES(33,'+917396019228','462442','2025-05-05 03:02:40','2025-05-05T03:12:40.756Z',1,0);
-- INSERT INTO otp_verifications VALUES(34,'+917396019228','165478','2025-05-06 02:27:45','2025-05-06T02:37:45.854Z',1,0);
-- INSERT INTO otp_verifications VALUES(35,'+917396019228','230236','2025-05-06 03:33:01','2025-05-06T03:43:01.515Z',1,0);
-- INSERT INTO otp_verifications VALUES(36,'+917396019228','592700','2025-05-09 09:31:28','2025-05-09T09:41:28.215Z',1,0);
-- INSERT INTO otp_verifications VALUES(37,'+917396019228','704012','2025-05-13 14:58:19','2025-05-13T15:08:19.725Z',1,0);
-- INSERT INTO otp_verifications VALUES(38,'+917396019228','724536','2025-05-13 15:47:45','2025-05-13T15:57:45.302Z',1,0);
-- INSERT INTO otp_verifications VALUES(39,'+917396019228','924233','2025-05-14 04:02:04','2025-05-14T04:12:04.179Z',1,0);
-- INSERT INTO otp_verifications VALUES(40,'+917396019228','522077','2025-05-14 04:06:32','2025-05-14T04:16:32.951Z',1,0);
-- INSERT INTO otp_verifications VALUES(41,'+917396019228','326774','2025-05-14 04:45:18','2025-05-14T04:55:18.797Z',1,0);
-- INSERT INTO otp_verifications VALUES(42,'+917396019228','932548','2025-05-14 05:09:28','2025-05-14T05:19:28.067Z',1,0);
-- INSERT INTO otp_verifications VALUES(43,'+917396019228','835024','2025-05-14 05:18:24','2025-05-14T05:28:24.922Z',1,0);
-- INSERT INTO otp_verifications VALUES(44,'+917396019228','416695','2025-05-15 04:15:08','2025-05-15T04:25:08.571Z',1,0);
-- INSERT INTO otp_verifications VALUES(45,'+917396019228','182687','2025-05-15 04:36:57','2025-05-15T04:46:57.648Z',1,0);
-- INSERT INTO otp_verifications VALUES(46,'+917396019228','297451','2025-05-15 04:39:46','2025-05-15T04:49:46.342Z',1,0);
-- INSERT INTO otp_verifications VALUES(47,'+917396019228','470553','2025-05-22 04:06:56','2025-05-22T04:16:55.988Z',1,0);
-- CREATE TABLE coupons (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   code TEXT NOT NULL UNIQUE,
--   type TEXT NOT NULL,
--   value DECIMAL(10, 2),
--   description TEXT NOT NULL,
--   min_order_amount DECIMAL(10, 2) DEFAULT 0,
--   max_discount DECIMAL(10, 2),
--   is_active BOOLEAN DEFAULT 1,
--   start_date TIMESTAMP,
--   end_date TIMESTAMP,
--   usage_limit INTEGER,
--   user_limit INTEGER DEFAULT 1,
--   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );
-- -- Educational Coupons for Sreekar Publishers
-- INSERT INTO coupons VALUES(1,'STUDENT10','percent',10,'10% off for new students',0,100,1,'2025-01-01','2025-12-31',999,1,'2025-01-15 10:00:00');
-- INSERT INTO coupons VALUES(2,'GRADE10PREP','percent',15,'15% off on Grade 10 board exam books',500,150,1,'2025-01-01','2025-03-31',200,1,'2025-01-15 10:00:00');
-- INSERT INTO coupons VALUES(3,'COMPLETESET','flat',100,'₹100 off on complete grade book sets',1000,NULL,1,'2025-01-01','2025-12-31',500,1,'2025-01-15 10:00:00');
-- INSERT INTO coupons VALUES(4,'NEWACADEMIC','percent',20,'20% off for new academic year',800,200,1,'2025-06-01','2025-07-31',300,1,'2025-01-15 10:00:00');
-- INSERT INTO coupons VALUES(5,'TELUGU50','flat',50,'₹50 off on Telugu medium books',300,NULL,1,'2025-01-01','2025-12-31',1000,1,'2025-01-15 10:00:00');
-- INSERT INTO coupons VALUES(6,'ENGLISH50','flat',50,'₹50 off on English medium books',300,NULL,1,'2025-01-01','2025-12-31',1000,1,'2025-01-15 10:00:00');
-- CREATE TABLE orders (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   user_id INTEGER NOT NULL,
--   order_number TEXT NOT NULL UNIQUE,
--   total_amount REAL NOT NULL,
--   delivery_fee REAL DEFAULT 0.0,
--   discount_amount REAL DEFAULT 0.0,
--   coupon_code TEXT,
--   payment_method TEXT NOT NULL,
--   payment_status TEXT NOT NULL,
--   order_status TEXT NOT NULL,
--   address_id INTEGER NOT NULL,
--   estimated_delivery TEXT,
--   delivered_at TEXT,
--   cancel_reason TEXT,
--   created_at TEXT NOT NULL DEFAULT (datetime('now')),
--   updated_at TEXT NOT NULL DEFAULT (datetime('now')), location_id INTEGER, delivery_boy_id INTEGER, out_for_delivery_at TEXT,
--   FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
--   FOREIGN KEY (address_id) REFERENCES user_addresses (id)
-- );
-- INSERT INTO orders VALUES(21,6,'OD9162177739',302.99,2.99,0,NULL,'cash','pending','placed',12,'39 minutes',NULL,NULL,'2025-05-14 05:18:36','2025-05-14 05:18:36',NULL,NULL,NULL);
-- INSERT INTO orders VALUES(22,6,'OD0829343495',102.99,2.99,0,NULL,'cash','cancelled','cancelled',11,'40 minutes','2025-05-14T05:39:52.319Z',NULL,'2025-05-14 05:21:22','2025-05-14 05:40:00',5,6,'2025-05-14T05:39:47.352Z');
-- INSERT INTO orders VALUES(23,6,'OD6102041269',541,1,60,'WELCOME10','cash','pending','placed',12,'43 minutes',NULL,NULL,'2025-05-15 04:50:10','2025-05-15 04:50:10',4,NULL,NULL);
-- INSERT INTO orders VALUES(24,6,'OD7560822040',51,1,0,NULL,'online','pending','placed',12,'30 minutes',NULL,NULL,'2025-05-15 05:09:16','2025-05-15 05:09:16',5,NULL,NULL);
-- INSERT INTO orders VALUES(25,6,'OD9513675648',51,1,0,NULL,'cash','pending','placed',11,'38 minutes',NULL,NULL,'2025-05-15 05:12:31','2025-05-15 05:12:31',5,NULL,NULL);
-- INSERT INTO orders VALUES(26,6,'OD0305852249',51,1,0,NULL,'online','pending','placed',12,'37 minutes',NULL,NULL,'2025-05-15 05:13:50','2025-05-15 05:13:50',5,NULL,NULL);
-- INSERT INTO orders VALUES(27,6,'OD0360242465',51,1,0,NULL,'cash','pending','placed',12,'31 minutes',NULL,NULL,'2025-05-15 05:13:56','2025-05-15 05:13:56',5,NULL,NULL);
-- INSERT INTO orders VALUES(28,6,'OD1655785064',51,1,0,NULL,'online','pending','placed',11,'38 minutes',NULL,NULL,'2025-05-15 05:16:05','2025-05-15 05:16:05',4,NULL,NULL);
-- INSERT INTO orders VALUES(29,6,'OD1736320143',51,1,0,NULL,'cash','pending','placed',11,'34 minutes',NULL,NULL,'2025-05-15 05:16:13','2025-05-15 05:16:13',4,NULL,NULL);
-- CREATE TABLE order_items (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   order_id INTEGER NOT NULL,
--   product_id INTEGER NOT NULL,
--   product_name TEXT NOT NULL,
--   product_price REAL NOT NULL,
--   quantity INTEGER NOT NULL DEFAULT 1,
--   total_price REAL NOT NULL,
--   created_at TEXT NOT NULL DEFAULT (datetime('now')),
--   FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
--   FOREIGN KEY (product_id) REFERENCES products (id)
-- );
-- INSERT INTO order_items VALUES(23,21,19,'Chicken Bone Less Pickel',100,3,300,'2025-05-14 05:18:36');
-- INSERT INTO order_items VALUES(24,22,19,'Chicken Bone Less Pickel',100,1,100,'2025-05-14 05:21:22');
-- INSERT INTO order_items VALUES(25,23,19,'Chicken Bone Less Pickel',100,6,600,'2025-05-15 04:50:10');
-- INSERT INTO order_items VALUES(26,24,20,'Chicken Bone Less Pickel',50,1,50,'2025-05-15 05:09:16');
-- INSERT INTO order_items VALUES(27,25,20,'Chicken Bone Less Pickel',50,1,50,'2025-05-15 05:12:31');
-- INSERT INTO order_items VALUES(28,26,20,'Chicken Bone Less Pickel',50,1,50,'2025-05-15 05:13:50');
-- INSERT INTO order_items VALUES(29,27,20,'Chicken Bone Less Pickel',50,1,50,'2025-05-15 05:13:56');
-- INSERT INTO order_items VALUES(30,28,20,'Chicken Bone Less Pickel',50,1,50,'2025-05-15 05:16:05');
-- INSERT INTO order_items VALUES(31,29,20,'Chicken Bone Less Pickel',50,1,50,'2025-05-15 05:16:13');
-- CREATE TABLE payment_transactions (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   order_id INTEGER NOT NULL,
--   payment_id TEXT NOT NULL UNIQUE,
--   payment_method TEXT NOT NULL,
--   amount REAL NOT NULL,
--   status TEXT NOT NULL,
--   gateway_response TEXT,
--   created_at TEXT NOT NULL DEFAULT (datetime('now')),
--   updated_at TEXT NOT NULL DEFAULT (datetime('now')),
--   FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE
-- );
-- CREATE TABLE order_locations (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   name TEXT NOT NULL,
--   address TEXT NOT NULL,
--   is_active BOOLEAN DEFAULT 1,
--   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );
-- INSERT INTO order_locations VALUES(4,'test test  aj','Bhimavaram',1,'2025-05-14 04:01:43');
-- INSERT INTO order_locations VALUES(5,'test 2','test 2',1,'2025-05-14 04:02:25');
-- CREATE TABLE delivery_boy_locations (
--   id INTEGER PRIMARY KEY AUTOINCREMENT,
--   user_id INTEGER NOT NULL,
--   location_id INTEGER NOT NULL,
--   is_active BOOLEAN DEFAULT 1,
--   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--   FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
--   FOREIGN KEY (location_id) REFERENCES order_locations (id) ON DELETE CASCADE
-- );
-- INSERT INTO delivery_boy_locations VALUES(6,6,4,1,'2025-05-14 05:10:13');
-- CREATE TABLE delivery_fee_settings (
--         id INTEGER PRIMARY KEY CHECK (id = 1),
--         base_fee DECIMAL(10, 2) NOT NULL DEFAULT 2.99,
--         free_delivery_threshold DECIMAL(10, 2) NOT NULL DEFAULT 0,
--         is_enabled BOOLEAN DEFAULT 1,
--         updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
--       );
-- INSERT INTO delivery_fee_settings VALUES(1,1,0,1,'2025-05-15T05:08:51.457Z');
-- CREATE TABLE location_delivery_fees (
--         id INTEGER PRIMARY KEY AUTOINCREMENT,
--         location_id INTEGER NOT NULL UNIQUE,
--         fee DECIMAL(10, 2) NOT NULL,
--         free_delivery_threshold DECIMAL(10, 2) NOT NULL DEFAULT 0,
--         updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--         FOREIGN KEY (location_id) REFERENCES order_locations(id) ON DELETE CASCADE
--       );
-- CREATE TABLE coupon_usages (
--         id INTEGER PRIMARY KEY AUTOINCREMENT,
--         coupon_code TEXT NOT NULL,
--         user_id INTEGER NOT NULL,
--         used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--         UNIQUE(coupon_code, user_id)
--       );
-- INSERT INTO coupon_usages VALUES(1,'WELCOME10',6,'2025-05-15 04:50:10');
-- CREATE TABLE payment_method_settings (
--         id INTEGER PRIMARY KEY CHECK (id = 1),
--         online_payment_enabled BOOLEAN DEFAULT 1,
--         cash_on_delivery_enabled BOOLEAN DEFAULT 1,
--         updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
--       );
-- INSERT INTO payment_method_settings VALUES(1,1,1,'2025-05-15T05:34:38.125Z');
-- DELETE FROM sqlite_sequence;
-- INSERT INTO sqlite_sequence VALUES('categories',11);
-- INSERT INTO sqlite_sequence VALUES('products',20);
-- INSERT INTO sqlite_sequence VALUES('product_images',5);
-- INSERT INTO sqlite_sequence VALUES('product_tags',10);
-- INSERT INTO sqlite_sequence VALUES('promotions',3);
-- INSERT INTO sqlite_sequence VALUES('users',7);
-- INSERT INTO sqlite_sequence VALUES('user_addresses',12);
-- INSERT INTO sqlite_sequence VALUES('otp_verifications',47);
-- INSERT INTO sqlite_sequence VALUES('coupons',6);
-- INSERT INTO sqlite_sequence VALUES('orders',29);
-- INSERT INTO sqlite_sequence VALUES('order_items',31);
-- INSERT INTO sqlite_sequence VALUES('order_locations',5);
-- INSERT INTO sqlite_sequence VALUES('delivery_boy_locations',6);
-- INSERT INTO sqlite_sequence VALUES('location_delivery_fees',2);
-- INSERT INTO sqlite_sequence VALUES('coupon_usages',1);
-- CREATE INDEX idx_otp_phone_number ON otp_verifications(phone_number);
-- CREATE INDEX idx_orders_user_id ON orders (user_id);
-- CREATE INDEX idx_orders_order_number ON orders (order_number);
-- CREATE INDEX idx_orders_order_status ON orders (order_status);
-- CREATE INDEX idx_orders_payment_status ON orders (payment_status);
-- CREATE INDEX idx_order_items_order_id ON order_items (order_id);
-- CREATE INDEX idx_payment_transactions_order_id ON payment_transactions (order_id);
-- CREATE INDEX idx_payment_transactions_payment_id ON payment_transactions (payment_id);
-- CREATE INDEX idx_order_locations_is_active ON order_locations(is_active);
-- CREATE INDEX idx_delivery_boy_locations_user_id ON delivery_boy_locations(user_id);
-- CREATE INDEX idx_delivery_boy_locations_location_id ON delivery_boy_locations(location_id);
-- CREATE TRIGGER update_orders_timestamp
-- AFTER UPDATE ON orders
-- BEGIN
--   UPDATE orders SET updated_at = datetime('now') WHERE id = NEW.id;
-- END;
-- CREATE TRIGGER update_payment_transactions_timestamp
-- AFTER UPDATE ON payment_transactions
-- BEGIN
--   UPDATE payment_transactions SET updated_at = datetime('now') WHERE id = NEW.id;
-- END;

/**
  district TEXT NOT NULL,
  nearest_busstand TEXT,
  school_name TEXT,
  whatsapp_number TEXT,
*/
-- ALTER TABLE user_addresses ADD COLUMN district TEXT ;
-- ALTER TABLE user_addresses ADD COLUMN nearest_busstand TEXT;
-- ALTER TABLE user_addresses ADD COLUMN school_name TEXT;
-- ALTER TABLE user_addresses ADD COLUMN whatsapp_number TEXT;

 ALTER TABLE products ADD COLUMN order_number INTEGER ;