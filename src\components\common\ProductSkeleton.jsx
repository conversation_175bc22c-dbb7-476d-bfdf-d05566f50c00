import React from "react";

/**
 * A skeleton loading component for products
 * Used as a placeholder while fetching product data
 */
const ProductSkeleton = ({ count = 8 }) => {
  return (
    <div className="w-full">
      {/* Enhanced Category skeleton with shimmer effect */}
      <div className="px-2 pb-1 pt-1">
        <div className="overflow-x-auto scrollbar-hide mb-2">
          <div className="flex space-x-3 py-1">
            {[1, 2, 3, 4, 5].map((item) => (
              <div key={item} className="flex-shrink-0">
                <div className="h-12 w-28 rounded-xl bg-gradient-to-r from-blue-100 to-blue-200 animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Enhanced Filter chips skeleton */}
      <div className="px-4 py-2">
        <div className="flex flex-wrap gap-2 mb-4">
          {[1, 2].map((item) => (
            <div key={item} className="h-8 w-24 bg-gradient-to-r from-gray-100 to-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>

        {/* Enhanced Results stats skeleton */}
        <div className="flex flex-wrap items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 rounded-xl mb-4 border border-blue-100">
          <div className="h-4 w-40 bg-gradient-to-r from-blue-200 to-blue-300 rounded-full animate-pulse"></div>
          <div className="h-4 w-28 bg-gradient-to-r from-blue-200 to-blue-300 rounded-full animate-pulse"></div>
        </div>

        {/* Enhanced Products grid skeleton with professional styling */}
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-20">
          {Array(count)
            .fill(0)
            .map((_, index) => (
              <div
                key={`skeleton-${index}`}
                className="product-skeleton bg-white rounded-2xl overflow-hidden shadow-md border border-gray-100 hover:shadow-lg transition-shadow duration-300"
              >
                <div className="w-full aspect-square bg-gradient-to-br from-gray-100 to-gray-200 animate-pulse relative">
                  {index % 3 === 0 && (
                    <div className="absolute top-3 right-3 h-6 w-14 bg-gradient-to-r from-orange-200 to-red-200 rounded-full animate-pulse"></div>
                  )}
                  {/* Shimmer overlay effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                </div>
                <div className="p-4">
                  <div className="h-3 w-20 bg-gradient-to-r from-blue-100 to-blue-200 rounded-full mb-3 animate-pulse"></div>
                  <div className="h-4 w-full bg-gradient-to-r from-gray-200 to-gray-300 rounded-full mb-2 animate-pulse"></div>
                  <div className="h-4 w-3/4 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full mb-3 animate-pulse"></div>
                  <div className="flex justify-between items-center pt-2">
                    <div className="h-6 w-20 bg-gradient-to-r from-green-100 to-green-200 rounded-full animate-pulse"></div>
                    <div className="h-9 w-9 bg-gradient-to-r from-blue-100 to-blue-200 rounded-xl animate-pulse"></div>
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>

      {/* Custom shimmer animation styles */}
      <style jsx>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }

        .animate-shimmer {
          animation: shimmer 2s infinite;
        }
      `}</style>
    </div>
  );
};

export default ProductSkeleton;