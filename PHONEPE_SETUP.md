# PhonePe Payment Gateway Integration Setup Guide

This guide will help you set up PhonePe payment gateway integration for your e-commerce application.

## Prerequisites

1. **PhonePe Merchant Account**: You need a verified PhonePe business account
2. **Business Documents**: PAN card, business registration documents
3. **Bank Account**: Active business bank account for settlements

## Step 1: Register with PhonePe

### Production Environment

1. **Visit PhonePe Business Portal**
   - Go to [business.phonepe.com](https://business.phonepe.com)
   - Click on "Get Started" or "Merchant Onboarding"

2. **Complete Merchant Registration**
   - Provide business details (name, address, contact)
   - Upload required documents (PAN, incorporation certificate, etc.)
   - Add bank account details for settlements

3. **Get API Credentials**
   - After approval, you'll receive:
     - **Merchant ID**: Your unique merchant identifier
     - **Salt Key**: Secret key for signature generation
     - **Salt Index**: Index number for the salt key

### Sandbox Environment (For Testing)

1. **Access PhonePe Sandbox**
   - Go to [developer.phonepe.com](https://developer.phonepe.com)
   - Sign up for developer account
   - Access sandbox credentials

2. **Test Credentials** (Pre-configured in this project):
   - **Merchant ID**: `MERCHANTUAT`
   - **Salt Key**: `099eb0cd-02cf-4e2a-8aca-3e6c6aff0399`
   - **Salt Index**: `1`
   - **API URL**: `https://api-preprod.phonepe.com/apis/pg-sandbox`

## Step 2: Configure Environment Variables

### For Development

Create a `.env` file in your project root:

```env
# PhonePe Sandbox Configuration (for testing)
PHONEPE_API_URL=https://api-preprod.phonepe.com/apis/pg-sandbox
PHONEPE_MERCHANT_ID=MERCHANTUAT
PHONEPE_SALT_KEY=099eb0cd-02cf-4e2a-8aca-3e6c6aff0399
PHONEPE_SALT_INDEX=1
```

### For Production (Cloudflare Workers)

#### Method 1: Using Wrangler CLI (Recommended for sensitive data)

```bash
# Set sensitive credentials as secrets
npx wrangler secret put PHONEPE_SALT_KEY
npx wrangler secret put PHONEPE_MERCHANT_ID

# Set non-sensitive variables in wrangler.jsonc
```

#### Method 2: Update wrangler.jsonc

```json
{
  "vars": {
    "PHONEPE_API_URL": "https://api.phonepe.com/apis/hermes",
    "PHONEPE_MERCHANT_ID": "YOUR_PRODUCTION_MERCHANT_ID",
    "PHONEPE_SALT_KEY": "YOUR_PRODUCTION_SALT_KEY",
    "PHONEPE_SALT_INDEX": "YOUR_PRODUCTION_SALT_INDEX"
  }
}
```

⚠️ **Security Note**: Always use secrets for production salt keys.

## Step 3: Database Setup

The required database tables are automatically created:

### payment_transactions Table
```sql
CREATE TABLE IF NOT EXISTS payment_transactions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  order_id INTEGER NOT NULL,
  transaction_id TEXT NOT NULL UNIQUE,
  payment_method TEXT NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  status TEXT NOT NULL,
  gateway_response TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

## Step 4: Testing the Integration

### 1. Test Payment Flow

1. **Place an order** with online payment method
2. **Verify payment initiation**:
   ```javascript
   // Check browser console for logs
   console.log('Payment initiated:', response);
   ```

3. **Complete payment** in sandbox using test cards:
   - **Success**: Use any valid card details in sandbox
   - **Failure**: Use specific test card numbers for failure scenarios

### 2. Test Webhook

1. **Webhook URL**: `https://yourdomain.com/api/payments/webhook`
2. **Webhook Events**: PhonePe will send payment status updates
3. **Verify webhook processing**:
   ```bash
   # Check application logs
   npx wrangler tail
   ```

### 3. Test Status Page

Visit: `https://yourdomain.com/checkout/status?transactionId=TXN_123456`

## Step 5: Production Deployment

### 1. Update Environment Variables

Replace sandbox credentials with production values:

```bash
# Update production environment
npx wrangler secret put PHONEPE_SALT_KEY
# Enter your production salt key when prompted

npx wrangler secret put PHONEPE_MERCHANT_ID
# Enter your production merchant ID when prompted
```

### 2. Update API URLs

```json
{
  "vars": {
    "PHONEPE_API_URL": "https://api.phonepe.com/apis/hermes"
  }
}
```

### 3. Configure Webhook URL

In PhonePe merchant dashboard:
1. Go to API Configuration
2. Set webhook URL: `https://yourdomain.com/api/payments/webhook`
3. Enable required webhook events

### 4. SSL Certificate

Ensure your domain has a valid SSL certificate (required for production).

## Step 6: Monitoring and Logs

### 1. Enable Logging

```javascript
// In webhook handler
console.log('Webhook received:', webhookData);
console.log('Payment status:', paymentState);
```

### 2. Monitor Transactions

Check payment transactions in your admin panel:
- Go to `/admin/orders`
- View payment status and transaction details

### 3. Handle Errors

Common error scenarios:
- **Invalid signature**: Check salt key and signature calculation
- **Transaction not found**: Verify transaction ID mapping
- **Network timeout**: Implement retry logic

## API Endpoints

### 1. Initiate Payment
- **URL**: `/api/payments/initiate`
- **Method**: `POST`
- **Body**: `{ "order_id": 123 }`

### 2. Check Payment Status
- **URL**: `/api/payments/status?transactionId=TXN_123`
- **Method**: `GET`

### 3. Webhook Handler
- **URL**: `/api/payments/webhook`
- **Method**: `POST`
- **Body**: PhonePe webhook payload

## Security Features

### 1. Signature Verification

```javascript
// Webhook signature verification (automatically handled)
const calculatedSignature = crypto
  .createHash('sha256')
  .update(rawBody + SALT_KEY)
  .digest('hex') + '###' + SALT_INDEX;
```

### 2. Environment-based Security

- **Development**: Signature verification with logging
- **Production**: Strict signature verification

### 3. Transaction Security

- Unique transaction IDs for each payment
- Order validation before payment initiation
- User authentication required

## Troubleshooting

### Common Issues

1. **Payment Initiation Fails**
   - Check merchant ID and salt key
   - Verify API URL for environment
   - Check order status and user permissions

2. **Webhook Not Received**
   - Verify webhook URL in PhonePe dashboard
   - Check SSL certificate
   - Review application logs

3. **Signature Verification Fails**
   - Ensure salt key matches exactly
   - Check salt index value
   - Verify request body parsing

### Debug Mode

Enable debug logging in development:

```javascript
if (process.env.NODE_ENV === 'development') {
  console.log('PhonePe Request:', phonepePayload);
  console.log('Signature:', checksum);
}
```

## Support and Documentation

- **PhonePe Developer Docs**: [developer.phonepe.com](https://developer.phonepe.com)
- **API Reference**: [PhonePe API Documentation](https://developer.phonepe.com/v1/docs/)
- **Support**: Contact PhonePe merchant support

## Next Steps

1. **Go Live Checklist**
   - [ ] Production credentials configured
   - [ ] Webhook URL updated
   - [ ] SSL certificate verified
   - [ ] Test transactions completed
   - [ ] Error handling implemented

2. **Advanced Features**
   - Implement payment method specific handling
   - Add payment analytics and reporting
   - Set up automated reconciliation
   - Implement refund functionality

3. **Monitoring**
   - Set up payment success/failure alerts
   - Monitor transaction volumes
   - Track payment gateway performance

## Integration Status

✅ **Payment Initiation**: Complete PhonePe API integration  
✅ **Webhook Handling**: Automated payment status updates  
✅ **Database Integration**: Transaction tracking and order updates  
✅ **Security**: Signature verification and environment-based controls  
✅ **User Interface**: Payment status page and checkout flow  
✅ **Error Handling**: Comprehensive error handling and logging  

The PhonePe payment gateway integration is now fully functional and ready for production use! 