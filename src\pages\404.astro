---
import MainLayout from "../layouts/MainLayout.astro";

// Get the current URL to provide context-aware suggestions
const url = new URL(Astro.request.url);
const path = url.pathname;

// Check if this is a product page
const isProductPage = path.startsWith('/product/');
const productSlug = isProductPage ? path.split('/').pop() : '';
---

<MainLayout
  title="Page Not Found - Sreekar Publishers"
  headerTitle="Page Not Found"
  showHeader={true}
  showBackButton={true}
  showFooter={true}
>
  <div class="max-w-lg mx-auto px-4 py-8 text-center">
    <div class="mb-8">
      <div class="w-28 h-28 bg-orange-50 rounded-full mx-auto flex items-center justify-center mb-6">
        <span class="material-icons-round text-orange-500 text-5xl">search_off</span>
      </div>
      <h1 class="text-3xl font-bold text-gray-800 mb-3">Page Not Found</h1>
      <p class="text-gray-600 mb-6 max-w-md mx-auto">
        {isProductPage
          ? `We couldn't find the product "${productSlug}". It may have been removed or is temporarily unavailable.`
          : "The page you're looking for doesn't exist or has been moved."}
      </p>
    </div>

    <div class="flex flex-col sm:flex-row gap-3 justify-center mb-10">
      <a
        href="/"
        class="inline-flex items-center justify-center px-5 py-2.5 bg-orange-600 text-white font-medium rounded-lg hover:bg-orange-700 transition-colors"
      >
        <span class="material-icons-round text-sm mr-1.5">home</span>
        Browse Products
      </a>

      <button
        onclick="window.history.back()"
        class="inline-flex items-center justify-center px-5 py-2.5 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 transition-colors"
      >
        <span class="material-icons-round text-sm mr-1.5">arrow_back</span>
        Go Back
      </button>
    </div>

    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      <div class="bg-orange-50 px-4 py-3 border-b border-orange-100">
        <h3 class="text-base font-medium text-gray-800">Popular Destinations</h3>
      </div>

      <div class="grid grid-cols-2 divide-x divide-y divide-gray-100">
        <a
          href="/"
          class="p-4 hover:bg-gray-50 transition-colors flex flex-col items-center"
        >
          <span class="material-icons-round text-orange-500 mb-2">restaurant_menu</span>
          <p class="font-medium text-gray-700">All Products</p>
        </a>
        <a
          href="/orders"
          class="p-4 hover:bg-gray-50 transition-colors flex flex-col items-center"
        >
          <span class="material-icons-round text-orange-500 mb-2">receipt_long</span>
          <p class="font-medium text-gray-700">Your Orders</p>
        </a>
        <a
          href="/cart"
          class="p-4 hover:bg-gray-50 transition-colors flex flex-col items-center"
        >
          <span class="material-icons-round text-orange-500 mb-2">shopping_cart</span>
          <p class="font-medium text-gray-700">Shopping Cart</p>
        </a>
        <a
          href="/profile"
          class="p-4 hover:bg-gray-50 transition-colors flex flex-col items-center"
        >
          <span class="material-icons-round text-orange-500 mb-2">person</span>
          <p class="font-medium text-gray-700">Your Profile</p>
        </a>
      </div>
    </div>

    <div class="mt-8 text-center">
      <p class="text-gray-500 text-sm">
        Need help? <a href="mailto:<EMAIL>" class="text-orange-600 hover:text-orange-700 font-medium">Contact Support</a>
      </p>
    </div>
  </div>

  <script>
    // Track 404 errors for analytics
    document.addEventListener('astro:page-load', () => {
      // Check if analytics is available
      if (typeof window !== 'undefined' && 'gtag' in window) {
        // @ts-ignore - gtag is added by Google Analytics script
        window.gtag('event', 'page_not_found', {
          event_category: 'Error',
          event_label: window.location.pathname,
          non_interaction: true
        });
      }
    });
  </script>
</MainLayout>
