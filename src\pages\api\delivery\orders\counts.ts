import type { APIRoute } from 'astro';
import { authMiddleware } from '../../../../middleware/auth';
import { getDeliveryBoyById } from '../../../../db/database';

export const prerender = false;

/**
 * Get order counts by status for a delivery boy
 */
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Authenticate user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    const { user } = authResult as any;

    // Check if the user is a delivery boy
    const deliveryBoy = await getDeliveryBoyById(locals.runtime.env, user.id);

    if (!deliveryBoy) {
      return new Response(JSON.stringify({ error: "Not authorized as delivery boy" }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get counts for each status
    const db = locals.runtime.env.SNACKSWIFT_DB;

    // Get processing orders count
    const processingResult = await db.prepare(`
      SELECT COUNT(*) as count
      FROM orders
      WHERE order_status = 'processing'
      AND delivery_boy_id = ?
    `).bind(user.id).all();

    // Get out_for_delivery orders count
    const outForDeliveryResult = await db.prepare(`
      SELECT COUNT(*) as count
      FROM orders
      WHERE order_status = 'out_for_delivery'
      AND delivery_boy_id = ?
    `).bind(user.id).all();

    // Get delivered orders count
    const deliveredResult = await db.prepare(`
      SELECT COUNT(*) as count
      FROM orders
      WHERE order_status = 'delivered'
      AND delivery_boy_id = ?
    `).bind(user.id).all();

    const counts = {
      processing: processingResult.results?.[0]?.count || 0,
      out_for_delivery: outForDeliveryResult.results?.[0]?.count || 0,
      delivered: deliveredResult.results?.[0]?.count || 0
    };

    return new Response(JSON.stringify({
      success: true,
      counts
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching order counts:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch order counts' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
