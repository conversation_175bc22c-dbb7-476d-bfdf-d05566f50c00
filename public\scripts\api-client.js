/**
 * Secure API client for Sreekar Publishers
 */
(function() {
  window.ApiClient = {
    /**
     * Make an authenticated API request
     */
    request: async function(endpoint, options = {}) {
      try {
        // Add authentication headers if available
        const headers = {
          'Content-Type': 'application/json',
          ...options.headers
        };

        // Try to get auth token from sessionStorage first (most secure)
        let token = sessionStorage.getItem('authToken');

        // If no auth token found, the user may not be logged in
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }

        // Make the request with authentication
        const response = await fetch(endpoint, {
          ...options,
          headers
        });

        // Handle 401 Unauthorized (session expired)
        if (response.status === 401) {
          // Clear stored auth data
          sessionStorage.removeItem('authToken');
          localStorage.removeItem('user');

          // Redirect to login page if unauthorized
          if (!endpoint.includes('/auth/')) {
            window.location.href = '/login?session_expired=true';
            throw new Error('Authentication required');
          }
        }

        return response;

      } catch (error) {
        console.error(`API request error (${endpoint}):`, error);
        throw error;
      }
    },

    /**
     * Get user addresses (authenticated)
     */
    getAddresses: async function() {
      const response = await this.request('/api/addresses');
      if (!response.ok) {
        throw new Error('Failed to fetch addresses');
      }
      return response.json();
    },

    /**
     * Add a new address (authenticated)
     */
    addAddress: async function(addressData) {
      const response = await this.request('/api/addresses', {
        method: 'POST',
        body: JSON.stringify(addressData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to add address');
      }

      return response.json();
    },

    /**
     * Update an existing address (authenticated)
     */
    updateAddress: async function(addressId, addressData) {
      const response = await this.request(`/api/addresses/${addressId}`, {
        method: 'PUT',
        body: JSON.stringify(addressData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update address');
      }

      return response.json();
    },

    /**
     * Delete an address (authenticated)
     */
    deleteAddress: async function(addressId) {
      const response = await this.request(`/api/addresses/${addressId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete address');
      }

      return response.json();
    },

    /**
     * Get order history (authenticated)
     * @param {string} status - Optional filter by order status
     */
    getOrders: async function(status = null) {
      let url = '/api/orders';
      if (status) {
        url += `?status=${encodeURIComponent(status)}`;
      }

      const response = await this.request(url);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch orders');
      }

      return response.json();
    },

    /**
     * Get order details (authenticated)
     */
    getOrderDetails: async function(orderId) {
      const response = await this.request(`/api/orders/${orderId}`);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch order details');
      }

      return response.json();
    },

    /**
     * Update user profile (authenticated)
     */
    updateProfile: async function(profileData) {
      const response = await this.request('/api/profile', {
        method: 'PUT',
        body: JSON.stringify(profileData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update profile');
      }

      const result = await response.json();

      // Update the stored user data with the new profile information
      if (result && result.user) {
        // Keep auth data but update user profile
        try {
          const currentUser = this.getCurrentUser();
          if (currentUser) {
            const updatedUser = {
              ...currentUser,
              name: result.user.name,
              email: result.user.email
            };
            localStorage.setItem('user', JSON.stringify(updatedUser));
          }
        } catch (error) {
          console.warn('Failed to update local user data', error);
        }
      }

      return result;
    },

    /**
     * Create a new order (authenticated)
     */
    createOrder: async function(orderData) {
      const response = await this.request('/api/orders', {
        method: 'POST',
        body: JSON.stringify(orderData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create order');
      }

      return response.json();
    },

    /**
     * Cancel an order (authenticated)
     */
    cancelOrder: async function(orderId, reason) {
      const response = await this.request(`/api/orders/${orderId}`, {
        method: 'PATCH',
        body: JSON.stringify({
          action: 'cancel',
          reason: reason || 'Cancelled by customer'
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to cancel order');
      }

      return response.json();
    },

    /**
     * Initiate payment for an order (authenticated)
     */
    initiatePayment: async function(orderId) {
      const response = await this.request('/api/payments/initiate', {
        method: 'POST',
        body: JSON.stringify({
          order_id: orderId
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to initiate payment');
      }

      return response.json();
    },

    /**
     * Check if user is authenticated
     */
    isAuthenticated: function() {
      // Primary check: look for auth token in sessionStorage
      const hasToken = !!sessionStorage.getItem('authToken');

      // Secondary check: look for user data in localStorage
      const hasUser = !!localStorage.getItem('user');

      // Tertiary check: look for session cookie as fallback
      const hasCookie = document.cookie.includes('session=');

      // Consider authenticated if we have token and either user data or cookie
      return hasToken && (hasUser || hasCookie);
    },

    /**
     * Get current user info
     */
    getCurrentUser: function() {
      try {
        const userJson = localStorage.getItem('user');
        return userJson ? JSON.parse(userJson) : null;
      } catch (error) {
        console.error('Error getting user info:', error);
        return null;
      }
    },

    /**
     * Logout user
     */
    logout: async function() {
      try {
        await this.request('/api/auth/logout', { method: 'POST' });
      } catch (error) {
        console.error('Error during logout API call:', error);
      } finally {
        // Clear stored auth data regardless of API success
        sessionStorage.removeItem('authToken');
        localStorage.removeItem('user');

        // Return true to indicate logout completed
        return true;
      }
    },

    /**
     * Check for session token in URL (for redirects after login)
     * This is useful when returning from external auth providers
     */
    handleAuthRedirect: function() {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        const userData = urlParams.get('user');

        if (token) {
          sessionStorage.setItem('authToken', token);

          // Try to parse and store user data if provided
          if (userData) {
            try {
              const user = JSON.parse(atob(userData));
              localStorage.setItem('user', JSON.stringify(user));
            } catch (e) {
              console.error('Failed to parse user data from URL');
            }
          }

          // Remove token from URL (replace state)
          const url = new URL(window.location);
          url.searchParams.delete('token');
          url.searchParams.delete('user');
          window.history.replaceState({}, document.title, url);

          return true;
        }

        return false;
      } catch (error) {
        console.error('Error handling auth redirect:', error);
        return false;
      }
    },

    /**
     * ADMIN API METHODS
     * The following methods are for admin dashboard functionality
     */

    /**
     * Get admin dashboard statistics
     */
    getAdminDashboardStats: async function(period = 'week', category = '', stockFilter = 'all') {
      let url = `/api/admin/dashboard/stats?period=${period}`;

      if (category) {
        url += `&category=${encodeURIComponent(category)}`;
      }

      if (stockFilter) {
        url += `&stockFilter=${encodeURIComponent(stockFilter)}`;
      }

      const response = await this.request(url);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch dashboard statistics');
      }

      return response.json();
    },

    /**
     * Get admin analytics data
     */
    getAdminAnalytics: async function(timeRange = 'week', dataType = 'revenue') {
      const response = await this.request(`/api/admin/dashboard/analytics?timeRange=${timeRange}&dataType=${dataType}`);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch analytics data');
      }

      return response.json();
    },

    /**
     * Get admin orders with pagination, filtering and search
     */
    getAdminOrders: async function(page = 1, limit = 20, status = 'all', search = '') {
      let url = `/api/admin/orders?page=${page}&limit=${limit}`;

      if (status !== 'all') {
        url += `&status=${encodeURIComponent(status)}`;
      }

      if (search) {
        url += `&search=${encodeURIComponent(search)}`;
      }

      const response = await this.request(url);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch admin orders');
      }

      return response.json();
    },

    /**
     * Get admin orders by date range
     */
    getAdminOrdersByDateRange: async function(fromDate, toDate, status = '', search = '') {
      let url = `/api/admin/orders-by-date?`;
      const params = new URLSearchParams();

      if (fromDate) {
        params.append('fromDate', fromDate);
      }

      if (toDate) {
        params.append('toDate', toDate);
      }

      if (status && status !== 'all') {
        params.append('status', status);
      }

      if (search) {
        params.append('search', search);
      }

      url += params.toString();

      const response = await this.request(url);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch orders by date range');
      }

      return response.json();
    },

    /**
     * Get admin order items for XLSX export
     */
    getAdminOrderItems: async function(orderIds) {
      const response = await this.request('/api/admin/order-items', {
        method: 'POST',
        body: JSON.stringify({ orderIds })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch order items');
      }

      return response.json();
    },

    /**
     * Get admin order details
     */
    getAdminOrderDetails: async function(orderId) {
      const response = await this.request(`/api/admin/order-details?orderId=${orderId}`);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch order details');
      }

      return response.json();
    },

    // updateOrderStatus function is now defined below

    /**
     * Bulk update order statuses (admin)
     */
    bulkUpdateOrderStatus: async function(orderIds, status) {
      const response = await this.request('/api/admin/orders', {
        method: 'PATCH',
        body: JSON.stringify({
          action: 'bulk_update_status',
          orderIds,
          status
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update orders');
      }

      return response.json();
    },



    /**
     * Admin: Update order status
     */
    updateOrderStatus: async function(orderId, status, cancelReason) {
      try {
        const payload = {
          action: 'update_status',
          status
        };

        // Add cancel_reason if status is cancelled and reason is provided
        if (status === 'cancelled' && cancelReason) {
          payload.reason = cancelReason;
        }

        const response = await fetch(`/api/admin/orders/${orderId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`Failed to update order status: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error updating order status:', error);
        throw error;
      }
    },

    /**
     * Get admin customers with pagination, filtering and search
     */
    getAdminCustomers: async function(page = 1, limit = 10, search = '', sortField = 'name', sortDirection = 'asc') {
      let url = `/api/admin/customers?page=${page}&limit=${limit}`;

      if (search) {
        url += `&search=${encodeURIComponent(search)}`;
      }

      if (sortField) {
        url += `&sortField=${encodeURIComponent(sortField)}`;
      }

      if (sortDirection) {
        url += `&sortDirection=${encodeURIComponent(sortDirection)}`;
      }

      const response = await this.request(url);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch customers');
      }

      return response.json();
    },

    /**
     * Admin: Delete an order
     */
    deleteOrder: async function(orderId) {
      try {
        const response = await fetch(`/api/admin/orders/${orderId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to delete order: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error deleting order:', error);
        throw error;
      }
    },

    /**
     * Admin: Check if user has admin access
     */
    checkAdminAccess: async function() {
      try {
        const response = await fetch('/api/auth/check-admin', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          return false;
        }

        const data = await response.json();
        return data.isAdmin === true;
      } catch (error) {
        console.error('Error checking admin access:', error);
        return false;
      }
    },    /**
     * Get delivery fee information (public) - simplified without location support
     */
    getDeliveryFees: async function() {
      try {
        const response = await fetch('/api/delivery-fees', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch delivery fees: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching delivery fees:', error);
        throw error;
      }
    },

    /**
     * Admin: Get all order locations (including inactive)
     */
    getAdminOrderLocations: async function() {
      try {
        const response = await fetch('/api/admin/locations', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch admin locations: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching admin order locations:', error);
        throw error;
      }
    },

    /**
     * Admin: Get order location by ID
     */
    getAdminOrderLocation: async function(locationId) {
      try {
        const response = await fetch(`/api/admin/locations/${locationId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch location: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching order location:', error);
        throw error;
      }
    },

    /**
     * Admin: Create a new order location
     */
    createOrderLocation: async function(locationData) {
      try {
        const response = await fetch('/api/admin/locations', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(locationData),
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`Failed to create location: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error creating order location:', error);
        throw error;
      }
    },

    /**
     * Admin: Update an order location
     */
    updateOrderLocation: async function(locationId, locationData) {
      try {
        const response = await fetch(`/api/admin/locations/${locationId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(locationData),
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`Failed to update location: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error updating order location:', error);
        throw error;
      }
    },

    /**
     * Admin: Delete an order location
     */
    deleteOrderLocation: async function(locationId) {
      try {
        const response = await fetch(`/api/admin/locations/${locationId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`Failed to delete location: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error deleting order location:', error);
        throw error;
      }
    },    /**
     * Get admin delivery boys
     */
    getAdminDeliveryBoys: async function() {
      const response = await this.request('/api/admin/delivery-boys');

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch delivery boys');
      }

      return response.json();
    },    /**
     * Add a new delivery boy (assign role to existing user)
     */
    addDeliveryBoy: async function(userId) {
      const response = await this.request('/api/admin/delivery-boys', {
        method: 'POST',
        body: JSON.stringify({
          userId
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to add delivery boy');
      }

      return response.json();
    },    /**
     * Update a delivery boy (remove role)
     */
    updateDeliveryBoy: async function(userId, action) {
      const payload = {
        userId,
        action
      };

      const response = await this.request('/api/admin/delivery-boys', {
        method: 'PATCH',
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update delivery boy');
      }

      return response.json();
    },    /**
     * Get delivery boy profile
     */
    getDeliveryProfile: async function() {
      const response = await this.request('/api/delivery/profile');

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch delivery profile');
      }

      return response.json();
    },

    /**
     * Get order counts by status for a delivery boy
     */
    getDeliveryOrderCounts: async function() {
      const response = await this.request('/api/delivery/orders/counts');

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch order counts');
      }

      return response.json();
    },

    /**
     * Get delivery orders (for delivery boy dashboard)
     */
    getDeliveryOrders: async function(status = null) {
      let url = '/api/delivery/orders';

      if (status) {
        url += `?status=${status}`;
      }

      const response = await this.request(url);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch delivery orders');
      }

      return response.json();
    },

    /**
     * Update delivery order (update status or assign to self)
     */
    updateDeliveryOrder: async function(orderId, action, status = null) {
      const payload = {
        orderId,
        action
      };

      if (status && action === 'update_status') {
        payload.status = status;
      }

      const response = await this.request('/api/delivery/orders', {
        method: 'PATCH',
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update delivery order');
      }

      return response.json();
    },

    /**
     * Admin: Get all coupons
     */
    getAdminCoupons: async function() {
      try {
        const response = await fetch('/api/admin/coupons', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch coupons: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching coupons:', error);
        throw error;
      }
    },

    /**
     * Admin: Get coupon by ID
     */
    getCoupon: async function(couponId) {
      try {
        const response = await fetch(`/api/admin/coupons/${couponId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch coupon: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching coupon:', error);
        throw error;
      }
    },

    /**
     * Admin: Create a new coupon
     */
    createCoupon: async function(couponData) {
      try {
        const response = await fetch('/api/admin/coupons', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(couponData),
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to create coupon: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error creating coupon:', error);
        throw error;
      }
    },

    /**
     * Admin: Update an existing coupon
     */
    updateCoupon: async function(couponId, couponData) {
      try {
        const response = await fetch(`/api/admin/coupons/${couponId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(couponData),
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to update coupon: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error updating coupon:', error);
        throw error;
      }
    },

    /**
     * Admin: Delete a coupon
     */
    deleteCoupon: async function(couponId) {
      try {
        const response = await fetch(`/api/admin/coupons/${couponId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to delete coupon: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error deleting coupon:', error);
        throw error;
      }
    },

    /**
     * Admin: Get delivery fee settings
     */
    getDeliveryFeeSettings: async function() {
      try {
        const response = await fetch('/api/admin/delivery-fees', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch delivery fee settings: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching delivery fee settings:', error);
        throw error;
      }
    },

    /**
     * Admin: Update delivery fee settings
     */
    updateDeliveryFeeSettings: async function(settingsData) {
      try {
        const response = await fetch('/api/admin/delivery-fees', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(settingsData),
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to update delivery fee settings: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error updating delivery fee settings:', error);
        throw error;
      }
    },    /**
     * Get all categories
     */
    getCategories: async function() {
      try {
        const response = await this.request('/api/admin/categories');

        if (!response.ok) {
          throw new Error(`Failed to fetch categories: ${response.statusText}`);
        }

        const data = await response.json();
        return { success: true, ...data };
      } catch (error) {
        console.error('Error fetching categories:', error);
        return { success: false, error: error.message, categories: [] };
      }
    },

    /**
     * Get payment method settings
     */
    getPaymentMethodSettings: async function() {
      try {
        const response = await this.request('/api/payment-methods');

        if (!response.ok) {
          throw new Error(`Failed to fetch payment method settings: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching payment method settings:', error);
        return {
          success: false,
          error: error.message,
          settings: {
            online_payment_enabled: true,
            cash_on_delivery_enabled: true
          }
        };
      }
    },

    /**
     * Update payment method settings (admin only)
     */
    updatePaymentMethodSettings: async function(settings) {
      try {
        const response = await this.request('/api/admin/payment-methods', {
          method: 'PUT',
          body: JSON.stringify(settings)
        });

        if (!response.ok) {
          throw new Error(`Failed to update payment method settings: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error updating payment method settings:', error);
        return { success: false, error: error.message };
      }
    },

    /**
     * Get payment method settings with category-specific overrides (admin only)
     */
    getPaymentMethodSettingsWithCategories: async function() {
      try {
        const response = await this.request('/api/admin/payment-methods?withCategories=true');

        if (!response.ok) {
          throw new Error(`Failed to fetch payment method settings with categories: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error fetching payment method settings with categories:', error);
        return {
          success: false,
          error: error.message,
          settings: {
            global: {
              online_payment_enabled: true,
              cash_on_delivery_enabled: true
            },
            categories: []
          }
        };
      }
    },

    /**
     * Update category-specific payment method settings (admin only)
     */
    updateCategoryPaymentMethodSettings: async function(categoryId, settings) {
      try {
        const response = await this.request('/api/admin/payment-methods', {
          method: 'PUT',
          body: JSON.stringify({
            category_id: categoryId,
            ...settings
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to update category payment method settings: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error updating category payment method settings:', error);
        return { success: false, error: error.message };
      }
    },

    /**
     * Delete category-specific payment method settings (reverts to global) (admin only)
     */
    deleteCategoryPaymentMethodSettings: async function(categoryId) {
      try {
        const response = await this.request('/api/admin/payment-methods', {
          method: 'PUT',
          body: JSON.stringify({
            category_id: categoryId,
            delete: true
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to delete category payment method settings: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error deleting category payment method settings:', error);
        return { success: false, error: error.message };
      }
    },

    /**
     * Get available payment methods based on cart contents (category-aware)
     */
    getCartPaymentMethods: async function(items) {
      try {
        const response = await this.request('/api/payment-methods/cart', {
          method: 'POST',
          body: JSON.stringify({ items })
        });

        if (!response.ok) {
          throw new Error(`Failed to get cart payment methods: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error getting cart payment methods:', error);
        return {
          success: false,
          error: error.message,
          settings: {
            online_payment_enabled: true,
            cash_on_delivery_enabled: true
          }
        };
      }
    },

    /**
     * Initiate a payment for an order
     */
    initiatePayment: async function(orderId) {
      try {
        const response = await this.request('/api/payments/initiate', {
          method: 'POST',
          body: JSON.stringify({ order_id: orderId })
        });

        if (!response.ok) {
          throw new Error(`Failed to initiate payment: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error initiating payment:', error);
        return {
          success: false,
          error: error.message
        };
      }
    },

    /**
     * Get payment status by transaction ID
     */
    getPaymentStatus: async function(transactionId) {
      try {
        const response = await this.request(`/api/payments/status?transactionId=${encodeURIComponent(transactionId)}`, {
          method: 'GET'
        });

        if (!response.ok) {
          throw new Error(`Failed to get payment status: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('Error getting payment status:', error);
        return {
          success: false,
          error: error.message
        };
      }
    },


  };

  // Check for auth redirect when script loads
  if (typeof window !== 'undefined') {
    window.ApiClient.handleAuthRedirect();
  }
})();
