import { getUserAddresses, addUserAddress } from '../../db/database';
import { authMiddleware } from '../../middleware/auth';

export const prerender = false;

/**
 * API endpoint to handle user addresses
 */
export async function GET({ locals, request }) {
  // Check authentication first
  const authResult = await authMiddleware({ request });
  if (authResult instanceof Response) {
    return authResult; // Return 401 if not authenticated
  }
  
  const user = authResult.user;
  
  if (!locals.runtime || !locals.runtime.env) {
    return new Response(JSON.stringify({ error: "Runtime environment not available" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // Use the authenticated user's ID
    const userId = user.id;
    
    const addresses = await getUserAddresses(locals.runtime.env, userId);
    return new Response(JSON.stringify({ addresses }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error("Error in addresses API:", error);
    return new Response(JSON.stringify({ error: "Failed to fetch addresses" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function POST({ locals, request }) {
  // Check authentication first
  const authResult = await authMiddleware({ request });
  if (authResult instanceof Response) {
    return authResult; // Return 401 if not authenticated
  }
  
  const user = authResult.user;
  
  if (!locals.runtime || !locals.runtime.env) {
    return new Response(JSON.stringify({ error: "Runtime environment not available" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // Use the authenticated user's ID
    const userId = user.id;
    
    const data = await request.json();
    
    // Validate required fields
    const requiredFields = ['full_name', 'phone', 'school_name', 'nearest_busstand', 'district', 'zip_code'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return new Response(JSON.stringify({ error: `${field} is required` }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // Create address object
    const addressData = {
      user_id: userId,
      full_name: data.full_name,
      phone: data.phone,
      school_name: data.school_name,
      nearest_busstand: data.nearest_busstand,
      district: data.district,
      zip_code: data.zip_code,
      is_default: data.is_default === true
    };

    const newAddress = await addUserAddress(locals.runtime.env, addressData);
    
    if (!newAddress) {
      return new Response(JSON.stringify({ error: "Failed to add address" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({ address: newAddress }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error("Error in addresses API:", error);
    return new Response(JSON.stringify({ error: "Failed to add address" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
