/**
 * Format a date string to IST timezone
 * @param {string} dateString - The date string in UTC
 * @param {Object} options - Formatting options
 * @returns {string} Formatted date string in IST
 */
export const formatToIST = (dateString, options = {}) => {
  if (!dateString) return "N/A";

  const defaultOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
    timeZone: 'Asia/Kolkata'
  };

  const date = new Date(dateString);
  return date.toLocaleString('en-IN', { ...defaultOptions, ...options });
};

/**
 * Format a date string to IST timezone with date only
 * @param {string} dateString - The date string in UTC
 * @returns {string} Formatted date string in IST
 */
export const formatDateToIST = (dateString) => {
  return formatToIST(dateString, {
    hour: undefined,
    minute: undefined,
    hour12: undefined
  });
};

/**
 * Format a date string to IST timezone with time only
 * @param {string} dateString - The date string in UTC
 * @returns {string} Formatted time string in IST
 */
export const formatTimeToIST = (dateString) => {
  return formatToIST(dateString, {
    year: undefined,
    month: undefined,
    day: undefined
  });
}; 