---
interface Props {
  title?: string;
  description?: string;
  showHeader?: boolean;
  showBackButton?: boolean;
  headerTitle?: string;
  showFooter?: boolean;
  image?: string;
  canonicalURL?: string;
  type?: 'website' | 'article' | 'product';
  keywords?: string;
  author?: string;
  publishDate?: Date;
  modifiedDate?: Date;
  schema?: any;
}

const {
  title = "Sreekar Publishers - Educational Materials",
  description = "Quality educational materials for students from 6th to 10th grade. Telugu and English Medium study resources, textbooks, and exam preparation materials.",
  showHeader = true,
  showBackButton = false,
  headerTitle = "", // Default is empty
  showFooter = true,
  image = "/images/highq-foods-social-image.jpg", // Default social image
  canonicalURL,
  type = "website",
  keywords = "educational materials, study resources, Telugu medium, English medium, 6th to 10th grade, textbooks, exam preparation, Sreekar Publishers, Eluru",
  author = "Sreekar Publishers Team",
  publishDate,
  modifiedDate,
  schema,
} = Astro.props;

// Import global styles
import "../styles/global.css";

// Safely generate URLs to avoid Invalid URL errors
const siteUrl = Astro.site ? Astro.site.toString() : 'https://sreekarpublishers.com/';
const baseUrl = siteUrl.endsWith('/') ? siteUrl : `${siteUrl}/`;
const currentPath = Astro.url.pathname;
const currentUrl = new URL(currentPath.startsWith('/') ? currentPath.slice(1) : currentPath, baseUrl);
const finalCanonicalURL = canonicalURL || currentUrl.href;

// Generate the absolute URL for the social image
let socialImageURL;
try {
  // Handle both absolute and relative image paths
  socialImageURL = image.startsWith('http') ? image : new URL(image.startsWith('/') ? image.slice(1) : image, baseUrl).href;
} catch (e) {
  // Fallback if URL creation fails
  socialImageURL = `${baseUrl}images/sreekar-publishers-social-image.png`;
}

// Schema.org JSON-LD
const defaultSchema = {
  "@context": "https://schema.org",
  "@type": "BookStore",
  "name": "Sreekar Publishers",
  "url": baseUrl,
  "logo": `${baseUrl}images/icons/icon-512x512.png`,
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "Door No: 2-56, CHANIKYAPURI COLONY 2ND LINE, 34 MC DIVISION, NEAR D MART",
    "addressLocality": "ELURU URBAN",
    "addressRegion": "ELURU",
    "postalCode": "534002",
    "addressCountry": "IN"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": "16.7107",
    "longitude": "81.1031"
  },
  "telephone": "+91-9392333935",
  "educationalUse": ["Study Materials", "Textbooks", "Reference Books", "Exam Preparation"],
  "priceRange": "₹₹",
  "openingHours": "Mo-Su 08:00-22:00",
  "sameAs": [
    "https://facebook.com/sreekarpublishers",
    "https://instagram.com/sreekarpublishers"
  ]
};

const jsonLD = schema || defaultSchema;

import PWAInstallPrompt from '../components/PWAInstallPrompt.astro';
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/png" href="/images/icons/icon-192x192.png" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no"
    />

    <!-- Primary Meta Tags -->
    <title>{title}</title>
    <meta name="title" content={title} />
    <meta name="description" content={description} />
    <meta name="keywords" content={keywords} />
    <meta name="author" content={author} />
    <link rel="canonical" href={finalCanonicalURL} />

    {publishDate && <meta name="date" content={publishDate.toISOString()} />}
    {modifiedDate && <meta name="revised" content={modifiedDate.toISOString()} />}

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content={type} />
    <meta property="og:url" content={finalCanonicalURL} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={socialImageURL} />
    <meta property="og:site_name" content="Sreekar Publishers" />
    <meta property="og:locale" content="en_IN" />
    <link rel="sitemap" href="/sitemap-index.xml" />
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content={finalCanonicalURL} />
    <meta name="twitter:title" content={title} />
    <meta name="twitter:description" content={description} />
    <meta name="twitter:image" content={socialImageURL} />
    <meta name="twitter:creator" content="@sreekarpublishers" />    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#FF6B35" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Sreekar Publishers" />
    <meta name="application-name" content="Sreekar Publishers" />
    <meta name="msapplication-TileColor" content="#FF6B35" />
    <meta name="msapplication-TileImage" content="/images/icons/icon-144x144.png" />
    <meta name="msapplication-config" content="/browserconfig.xml" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/images/icons/icon-192x192.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/images/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/icon-180x180.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/images/icons/icon-167x167.png" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" crossorigin="use-credentials" />

    <!-- Immediate Service Worker Registration -->
    <script is:inline>
      // Register Service Worker as early as possible
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js', {
            scope: '/'
          })
          .then(registration => {
            console.log('Service Worker registered successfully:', registration.scope);
            
            // Check if the service worker is active
            if (registration.active) {
              console.log('Service Worker is active');
            }
            
            // Listen for updates
            registration.addEventListener('updatefound', () => {
              const newWorker = registration.installing;
              console.log('Service Worker update found!');
              
              newWorker.addEventListener('statechange', () => {
                console.log('Service Worker state:', newWorker.state);
              });
            });
          })
          .catch(error => {
            console.error('Service Worker registration failed:', error);
          });
        });
      } else {
        console.log('Service Workers are not supported in this browser');
      }
    </script>

    <!-- Structured data -->
    <script type="application/ld+json" set:html={JSON.stringify(jsonLD)} />    <!-- Preload Critical Resources -->
    <link rel="preload" as="style" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" crossorigin />
    <link rel="preload" as="style" href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" crossorigin />

    <!-- Google Fonts for better typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!-- Material Icons for navigation -->
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons+Round"
      rel="stylesheet"
    />
  </head>
  <body class="flex flex-col bg-gray-50">
    <!-- Status Bar Spacer for iOS devices -->
    <div id="status-bar-spacer" class="w-full bg-white z-50"></div>

    <!-- Enhanced Professional Educational Header -->
    {
      showHeader && (
        <header
          id="app-header"
          class="sticky top-0 z-30 bg-white/95 backdrop-blur-md flex items-center justify-between border-b border-blue-100 shadow-sm"
        >
          <div class="w-full max-w-7xl mx-auto flex items-center justify-between px-4 h-16">
            <div class="flex items-center flex-1">
              {showBackButton ? (
                <button
                  onclick="history.back()"
                  class="flex items-center justify-center -ml-2 p-3 rounded-xl hover:bg-blue-50 active:bg-blue-100 transition-all duration-200 group"
                  aria-label="Go back"
                >
                  <span class="material-icons-round text-blue-600 group-hover:text-blue-700">
                    arrow_back
                  </span>
                </button>
              ) : (
                <div class="app-logo flex items-center">
                  <div class="w-12 h-12 rounded-2xl flex items-center justify-center shadow-md overflow-hidden bg-gradient-to-br from-blue-500 to-blue-600 ring-2 ring-blue-100">
                    <img
                      src="/images/icons/icon-192x192.png"
                      alt="Sreekar Publishers Logo"
                      width="48"
                      height="48"
                      class="w-full h-full object-cover"
                    />
                  </div>
                  {headerTitle ? (
                    <h1 class="text-xl font-bold text-gray-800 ml-4 tracking-tight">
                      {headerTitle}
                    </h1>
                  ) : (
                    <div class="ml-4">
                      <h1 class="text-xl font-bold text-gray-800 leading-tight tracking-tight">
                        Sreekar Publishers
                      </h1>
                      <p class="text-sm text-blue-600 font-medium -mt-0.5">
                        Educational Excellence
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div class="flex items-center gap-3">
              <a
                href="/cart"
                class="p-3 rounded-xl hover:bg-blue-50 active:bg-blue-100 transition-all duration-200 inline-flex items-center justify-center relative group"
                aria-label="Cart"
              >
                <span class="material-icons-round text-blue-600 group-hover:text-blue-700">
                  shopping_bag
                </span>
                <span
                  id="cart-badge"
                  class="absolute -top-1 -right-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full w-6 h-6 text-xs font-bold flex items-center justify-center border-2 border-white shadow-lg"
                  aria-hidden="true"
                >
                  0
                </span>
              </a>
            </div>
          </div>
        </header>
      )
    }

    <main class="flex-1 pb-20 pt-0">
      <slot />
    </main>

    <!-- Enhanced Professional Educational Bottom Navigation -->
    {
      showFooter && (
        <nav
          class="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-md flex justify-around items-center shadow-xl z-40 border-t border-blue-100"
          style="height: calc(68px + var(--safe-area-inset-bottom)); padding-bottom: var(--safe-area-inset-bottom);"
          aria-label="Main navigation"
        >
          <a
            href="/"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-2 pb-1 group"
            data-target="home"
            aria-label="Home"
          >
            <div class="nav-icon-container rounded-2xl w-12 h-12 flex items-center justify-center mb-1 transition-all duration-200 group-hover:bg-blue-50 group-active:bg-blue-100">
              <span class="material-icons-round text-[22px] text-blue-600 group-hover:text-blue-700">home</span>
            </div>
            <span class="text-[11px] font-medium text-blue-600 group-hover:text-blue-700">Home</span>
          </a>
          <a
            href="/favorites"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-2 pb-1 group"
            data-target="favorites"
            aria-label="Favorites"
          >
            <div class="nav-icon-container rounded-2xl w-12 h-12 flex items-center justify-center mb-1 transition-all duration-200 group-hover:bg-red-50 group-active:bg-red-100">
              <span class="material-icons-round text-[22px] text-gray-500 group-hover:text-red-500">favorite</span>
            </div>
            <span class="text-[11px] font-medium text-gray-500 group-hover:text-red-500">Favorites</span>
          </a>

          <a
            href="/orders"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-2 pb-1 group"
            data-target="projects"
            aria-label="Orders"
          >
            <div class="nav-icon-container rounded-2xl w-12 h-12 flex items-center justify-center mb-1 transition-all duration-200 group-hover:bg-green-50 group-active:bg-green-100">
              <span class="material-icons-round text-[22px] text-gray-500 group-hover:text-green-600">receipt_long</span>
            </div>
            <span class="text-[11px] font-medium text-gray-500 group-hover:text-green-600">Orders</span>
          </a>
          <a
            href="/cart"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-2 pb-1 group"
            data-target="cart"
            aria-label="Cart"
          >
            <div class="nav-icon-container rounded-2xl w-12 h-12 flex items-center justify-center mb-1 relative transition-all duration-200 group-hover:bg-orange-50 group-active:bg-orange-100">
              <span class="material-icons-round text-[22px] text-gray-500 group-hover:text-orange-600">
                shopping_cart
              </span>
              <span class="absolute -top-1 -right-1 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full w-5 h-5 text-[10px] font-bold flex items-center justify-center border-2 border-white shadow-md">
                3
              </span>
            </div>
            <span class="text-[11px] font-medium text-gray-500 group-hover:text-orange-600">Cart</span>
          </a>
          <a
            href="/profile"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-2 pb-1 group"
            data-target="profile"
            aria-label="Profile"
          >
            <div class="nav-icon-container rounded-2xl w-12 h-12 flex items-center justify-center mb-1 transition-all duration-200 group-hover:bg-purple-50 group-active:bg-purple-100">
              <span class="material-icons-round text-[22px] text-gray-500 group-hover:text-purple-600">person</span>
            </div>
            <span class="text-[11px] font-medium text-gray-500 group-hover:text-purple-600">Profile</span>
          </a>
          <a
            href="/policies"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-2 pb-1 group"
            data-target="policies"
            aria-label="Policies"
          >
            <div class="nav-icon-container rounded-2xl w-12 h-12 flex items-center justify-center mb-1 transition-all duration-200 group-hover:bg-blue-50 group-active:bg-blue-100">
              <span class="material-icons-round text-[22px] text-gray-500 group-hover:text-blue-600">policy</span>
            </div>
            <span class="text-[11px] font-medium text-gray-500 group-hover:text-blue-600">Policies</span>
          </a>
        </nav>
      )
    }

    <!-- App Scripts -->
    <script is:inline src="/scripts/app.js"></script>
    <script is:inline src="/scripts/swipe.js"></script>
    <script is:inline src="/scripts/utils-initializer.js"></script>

    <!-- Initial Setup Script -->
    <script is:inline>
      // Initial setup on first page load
      document.addEventListener("DOMContentLoaded", () => {
        // Initialize status bar, nav tabs, and touch feedback
        // These will be reinitialized on page transitions by transition-handler.js
        if (typeof initStatusBar === 'function') initStatusBar();
        if (typeof initNavTabs === 'function') initNavTabs();
        if (typeof initTouchFeedback === 'function') initTouchFeedback();
      });
    </script>

    <!-- Add utility scripts -->
    <script src="/scripts/cart-utils.js" is:inline></script>
    <script src="/scripts/favorites-utils.js" is:inline></script>
    <script src="/scripts/api-client.js" is:inline></script>
    <script src="/scripts/auth-utils.js" is:inline></script>

    <!-- Add transition handler scripts -->
    <script src="/scripts/transition-handler.js" is:inline></script>
    <script src="/scripts/react-transition-handler.js" is:inline></script>

    <!-- Add progress bar for page transitions -->
    <script src="/scripts/progress-bar.js" is:inline></script>

    <script is:inline>
      // Initialize on first page load
      document.addEventListener("DOMContentLoaded", () => {
        // Initialize utilities
        if (window.UtilsInitializer) {
          window.UtilsInitializer.initAllUtils().then(({ cartUtils }) => {
            if (cartUtils) {
              cartUtils.updateCartBadge();
            }
          });
        } else if (window.CartUtils) {
          window.CartUtils.updateCartBadge();
        }

        // Add scroll behavior for header
        const header = document.getElementById("app-header");
        if (header) {
          let lastScrollTop = 0;

          window.addEventListener("scroll", () => {
            const scrollTop =
              window.pageYOffset || document.documentElement.scrollTop;

            // Add shadow when scrolling down
            if (scrollTop > 10) {
              header.classList.add("shadow-sm");
            } else {
              header.classList.remove("shadow-sm");
            }

            lastScrollTop = scrollTop;
          });
        }
      });

      // Also initialize on Astro page transitions
      document.addEventListener("astro:page-load", () => {
        if (window.UtilsInitializer) {
          window.UtilsInitializer.initAllUtils().then(({ cartUtils }) => {
            if (cartUtils) {
              cartUtils.updateCartBadge();
            }
          });
        } else if (window.CartUtils) {
          window.CartUtils.updateCartBadge();
        }
      });
    </script>
    <PWAInstallPrompt />
    
    <!-- Floating WhatsApp Button -->
    <div 
      id="whatsapp-float"
      class="fixed bottom-24 right-4 z-50 sm:bottom-20"
    >
      <a
        href="https://wa.me/919392333935?text=Hello"
        target="_blank"
        rel="noopener noreferrer"
        class="flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95"
        aria-label="Chat on WhatsApp"
        title="Chat with us on WhatsApp"
      >
        <svg
          width="28"
          height="28"
          viewBox="0 0 24 24"
          fill="currentColor"
          class="drop-shadow-sm"
        >
          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.386"/>
        </svg>
      </a>
    </div>

    <!-- WhatsApp Button Animation CSS -->
    <style>
      #whatsapp-float {
        animation: whatsapp-pulse 2s infinite;
      }

      @keyframes whatsapp-pulse {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
        100% {
          transform: scale(1);
        }
      }

      #whatsapp-float:hover {
        animation: none;
      }

      /* Responsive adjustments */
      @media (max-width: 640px) {
        #whatsapp-float {
          right: 1rem;
          bottom: 6rem;
        }
      }

      @media (min-width: 641px) {
        #whatsapp-float {
          right: 1.5rem;
          bottom: 5rem;
        }
      }
    </style>

  </body>
</html>
