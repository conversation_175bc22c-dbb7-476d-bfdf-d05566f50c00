import type { APIRoute } from 'astro';
import { adminAuthMiddleware } from '../../../middleware/auth';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Parse query parameters
    const url = new URL(request.url);
    const fromDate = url.searchParams.get('fromDate') || null;
    const toDate = url.searchParams.get('toDate') || null;
    const status = url.searchParams.get('status') || null;
    const search = url.searchParams.get('search') || null;
    
    // Build query based on filters
    let query = `
      SELECT o.*, 
             u.email as user_email, 
             u.name as user_name, 
             u.phone_number as user_phone,
             ol.address as location_address,
             ua.full_name as shipping_full_name,
             ua.phone as shipping_phone,
             ua.address as shipping_address,
             ua.city as shipping_city,
             ua.zip_code as shipping_zip_code,
             ua.instructions as shipping_instructions
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      LEFT JOIN order_locations ol ON o.location_id = ol.id
      LEFT JOIN user_addresses ua ON o.address_id = ua.id
      WHERE 1=1
    `;
    
    const queryParams = [];
    
    // Add date range filters
    if (fromDate) {
      query += ` AND DATE(o.created_at) >= DATE(?)`;
      queryParams.push(fromDate);
    }
    
    if (toDate) {
      query += ` AND DATE(o.created_at) <= DATE(?)`;
      queryParams.push(toDate);
    }
    
    // Add status filter (supports single status or comma-separated multiple statuses)
    if (status && status !== 'all') {
      const statuses = status.split(',').map(s => s.trim()).filter(s => s.length > 0);
      if (statuses.length === 1) {
        query += ` AND o.order_status = ?`;
        queryParams.push(statuses[0]);
      } else if (statuses.length > 1) {
        const placeholders = statuses.map(() => '?').join(',');
        query += ` AND o.order_status IN (${placeholders})`;
        queryParams.push(...statuses);
      }
    }
    
    // Add search filter
    if (search) {
      query += ` AND (o.order_number LIKE ? OR u.email LIKE ? OR u.name LIKE ?)`;
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }
    
    // Order by creation date descending
    query += ` ORDER BY o.created_at DESC`;
    
    // Execute query
    const result = await locals.runtime.env.SNACKSWIFT_DB.prepare(query)
      .bind(...queryParams)
      .all();
    
    return new Response(
      JSON.stringify({
        success: true,
        orders: result.results || [],
        filters: {
          fromDate,
          toDate,
          status,
          search
        }
      }),
      { 
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
  } catch (error) {
    console.error('Error fetching orders by date range:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: 'Failed to fetch orders by date range' 
      }),
      { status: 500 }
    );
  }
}; 