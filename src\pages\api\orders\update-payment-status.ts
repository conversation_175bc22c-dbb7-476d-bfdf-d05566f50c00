import type { APIRoute } from "astro";

interface UpdatePaymentStatusRequest {
  orderId: string;
  status: "paid" | "pending";
}

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const { orderId, status } = (await request.json()) as UpdatePaymentStatusRequest;

    if (!orderId || !status) {
      return new Response(
        JSON.stringify({
          error: "Order ID and status are required",
        }),
        { status: 400 }
      );
    }

    // Get database from runtime environment
    const db = locals.runtime.env.SNACKSWIFT_DB;

    // Update the order's payment status
    const result = await db
      .prepare(
        `UPDATE orders 
         SET payment_status = ?, 
             updated_at = CURRENT_TIMESTAMP 
         WHERE id = ? 
         RETURNING *`
      )
      .bind(status, orderId)
      .first();

    if (!result) {
      return new Response(
        JSON.stringify({
          error: "Order not found",
        }),
        { status: 404 }
      );
    }

    return new Response(
      JSON.stringify({
        message: "Payment status updated successfully",
        order: result,
      }),
      { status: 200 }
    );
  } catch (error) {
    console.error("Error in update-payment-status:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
      }),
      { status: 500 }
    );
  }
}; 