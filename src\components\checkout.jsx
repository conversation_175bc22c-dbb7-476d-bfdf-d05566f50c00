import React, { useState, useEffect } from "react";
import LoadingOverlay from "./common/LoadingOverlay";

// Format currency values
const formatCurrency = (amount) => {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: 2,
  }).format(amount);
};

export default function Checkout() {
  const [cartItems, setCartItems] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingAddresses, setLoadingAddresses] = useState(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentMethodSettings, setPaymentMethodSettings] = useState({
    online_payment_enabled: true,
    cash_on_delivery_enabled: true,
  });
  const [pricing, setPricing] = useState({
    subtotal: 0,
    deliveryFee: 0,
    discount: 0,
    total: 0,
  });
  const [step, setStep] = useState(1);
  const [savedAddresses, setSavedAddresses] = useState([]);
  const [selectedAddressId, setSelectedAddressId] = useState(null);
  const [showNewAddressForm, setShowNewAddressForm] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("online");
  const [couponInfo, setCouponInfo] = useState(null);

  // Form states
  const [shippingInfo, setShippingInfo] = useState({
    fullName: "",
    phone: "",
    school_name: "",
    nearest_busstand: "",
    district: "",
    zipCode: "",
    saveAddress: true,
    setAsDefault: true,
  });
  const [shippingErrors, setShippingErrors] = useState({});

  // Calculate delivery fee based on subtotal
  const calculateDeliveryFee = (subtotal) => {
    return subtotal >= 1200 ? 0 : 150;
  };

  // Calculate pricing with delivery fee logic
  const calculatePricing = (items, currentDiscount = 0, couponCode = null) => {
    const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const deliveryFee = calculateDeliveryFee(subtotal);
    const total = subtotal + deliveryFee - currentDiscount;
    
    return {
      subtotal,
      deliveryFee,
      discount: currentDiscount,
      total: Math.max(0, total),
      isFreeDelivery: deliveryFee === 0,
      freeDeliveryThreshold: 1200,
      couponCode
    };
  };

  // Load cart items, pricing, addresses, locations, and payment method settings on component mount
  useEffect(() => {
    // Load cart items and pricing info
    const timer = setTimeout(async () => {
      if (typeof window !== "undefined" && window.CartUtils) {
        const items = window.CartUtils.getCartItems();
        const coupon = await window.CartUtils.getAppliedCoupon();
        
        // Calculate pricing with our delivery fee logic
        const discount = coupon ? coupon.discount_amount : 0;
        const couponCode = coupon ? coupon.code : null;
        const calculatedPricing = calculatePricing(items, discount, couponCode);

        setCartItems(items);
        setPricing(calculatedPricing);
        setCouponInfo(coupon);

        // Redirect to cart if empty
        if (items.length === 0) {
          window.location.href = "/cart";
          return;
        }
      }
      setIsLoading(false); // Load user addresses and payment method settings
      fetchUserAddresses();
      fetchPaymentMethodSettings();
      fetchUserPhone();
    }, 300);

    // Add event listener for pricing updates
    const handlePricingUpdate = (event) => {
      console.log("Checkout: Pricing updated event received:", event.detail);
      // Recalculate pricing with our delivery fee logic
      const items = window.CartUtils.getCartItems();
      const discount = couponInfo ? couponInfo.discount_amount : 0;
      const couponCode = couponInfo ? couponInfo.code : null;
      const calculatedPricing = calculatePricing(items, discount, couponCode);
      setPricing(calculatedPricing);
    };

    if (typeof window !== "undefined") {
      window.addEventListener("pricing-updated", handlePricingUpdate);
    }

    return () => {
      clearTimeout(timer);
      if (typeof window !== "undefined") {
        window.removeEventListener("pricing-updated", handlePricingUpdate);
      }
    };
  }, []);

  // Refresh payment method settings when cart items change (category-aware)
  useEffect(() => {
    if (cartItems.length > 0) {
      fetchPaymentMethodSettings();
    }
  }, [cartItems]);

  // Recalculate pricing when cart items change
  useEffect(() => {
    if (cartItems.length > 0) {
      const discount = couponInfo ? couponInfo.discount_amount : 0;
      const couponCode = couponInfo ? couponInfo.code : null;
      const calculatedPricing = calculatePricing(cartItems, discount, couponCode);
      setPricing(calculatedPricing);
    }
  }, [cartItems, couponInfo]);

  // Fetch payment method settings (category-aware based on cart)
  const fetchPaymentMethodSettings = async () => {
    try {
      // Use cart-aware payment method settings if cart has items
      if (cartItems.length > 0) {
        const response = await window.ApiClient.getCartPaymentMethods(
          cartItems
        );
        if (response.success && response.settings) {
          setPaymentMethodSettings(response.settings);

          // If the current payment method is disabled, switch to an enabled one
          if (
            paymentMethod === "online" &&
            !response.settings.online_payment_enabled
          ) {
            if (response.settings.cash_on_delivery_enabled) {
              setPaymentMethod("cash");
            }
          } else if (
            paymentMethod === "cash" &&
            !response.settings.cash_on_delivery_enabled
          ) {
            if (response.settings.online_payment_enabled) {
              setPaymentMethod("online");
            }
          }
        }
      } else {
        // Fallback to global settings if cart is empty
        const response = await window.ApiClient.getPaymentMethodSettings();
        if (response.success && response.settings) {
          setPaymentMethodSettings(response.settings);

          // If the current payment method is disabled, switch to an enabled one
          if (
            paymentMethod === "online" &&
            !response.settings.online_payment_enabled
          ) {
            if (response.settings.cash_on_delivery_enabled) {
              setPaymentMethod("cash");
            }
          } else if (
            paymentMethod === "cash" &&
            !response.settings.cash_on_delivery_enabled
          ) {
            if (response.settings.online_payment_enabled) {
              setPaymentMethod("online");
            }
          }
        }
      }
    } catch (error) {
      console.error("Error fetching payment method settings:", error);
      // Fallback to default settings on error
      setPaymentMethodSettings({
        online_payment_enabled: true,
        cash_on_delivery_enabled: true,
      });
    }
  };

  // Fetch user addresses from the API
  const fetchUserAddresses = async () => {
    setLoadingAddresses(true);
    try {
      // Check if the user is authenticated first
      if (typeof window !== "undefined" && window.ApiClient) {
        if (!window.ApiClient.isAuthenticated()) {
          // Redirect to login with return URL
          const currentPath = window.location.pathname;
          window.location.href = `/login?redirect=${encodeURIComponent(
            currentPath
          )}`;
          return;
        }

        // User is authenticated, fetch addresses
        const data = await window.ApiClient.getAddresses();

        if (data.addresses && Array.isArray(data.addresses)) {
          setSavedAddresses(data.addresses);

          // Select default address if available
          const defaultAddress = data.addresses.find((addr) => addr.is_default);
          if (defaultAddress) {
            setSelectedAddressId(defaultAddress.id);

            // Pre-populate shipping form with default address for easy editing
            setShippingInfo({
              fullName: defaultAddress.full_name,
              phone: defaultAddress.phone,
              school_name: defaultAddress.school_name,
              nearest_busstand: defaultAddress.nearest_busstand,
              district: defaultAddress.district,
              zipCode: defaultAddress.zip_code,
              saveAddress: false,
              setAsDefault: false,
            });
          } else if (data.addresses.length > 0) {
            // If no default, select the first address
            // setSelectedAddressId(data.addresses[0].id);
          }else{
            setShowNewAddressForm(true);
          }
        }
      } else {
        throw new Error("API client not available");
      }
    } catch (error) {
      console.error("Error fetching addresses:", error);
      // Check if it's an authentication error
      if (error.message === "Authentication required") {
        // Save current URL and redirect to login
        const currentPath = window.location.pathname;
        showNotification("Please log in to continue", "info");
        setTimeout(() => {
          window.location.href = `/login?redirect=${encodeURIComponent(
            currentPath
          )}`;
        }, 1500);
        return;
      }

      // Show a friendly error message for other errors
      showNotification("Could not load saved addresses", "error");
    } finally {
      setLoadingAddresses(false);
    }
  };

  // Save a new address
  const saveNewAddress = async () => {
    if (!validateShippingForm()) {
      return false;
    }

    // Check if user is authenticated
    if (
      typeof window !== "undefined" &&
      window.ApiClient &&
      !window.ApiClient.isAuthenticated()
    ) {
      // Save form data in sessionStorage to restore after login
      try {
        sessionStorage.setItem(
          "pendingAddressData",
          JSON.stringify(shippingInfo)
        );
      } catch (e) {
        console.error("Failed to save address data:", e);
      }

      // Redirect to login with return URL
      const currentPath = window.location.pathname;
      showNotification("Please log in to save addresses", "info");
      setTimeout(() => {
        window.location.href = `/login?redirect=${encodeURIComponent(
          currentPath
        )}`;
      }, 1500);
      return false;
    }

    setLoadingAddresses(true);
    try {
      const addressData = {
        full_name: shippingInfo.fullName,
        phone: shippingInfo.phone,
        school_name: shippingInfo.school_name,
        nearest_busstand: shippingInfo.nearest_busstand,
        district: shippingInfo.district,
        zip_code: shippingInfo.zipCode,
        is_default: shippingInfo.setAsDefault,
      };

      // Use the secure API client instead of fetch directly
      if (typeof window !== "undefined" && window.ApiClient) {
        const data = await window.ApiClient.addAddress(addressData);

        if (data.address) {
          // Update the addresses list
          await fetchUserAddresses();
          // Select the new address
          setSelectedAddressId(data.address.id);
          // Hide the form
          setShowNewAddressForm(false);

          showNotification("Address saved successfully");
          return true;
        }
      } else {
        throw new Error("API client not available");
      }

      return false;
    } catch (error) {
      console.error("Error saving address:", error);
      // Check if it's an authentication error
      if (error.message === "Authentication required") {
        showNotification("Please log in to save addresses", "error");
        setTimeout(() => {
          window.location.href = "/login?redirect=/checkout";
        }, 2000);
        return false;
      }

      showNotification("Could not save address", "error");
      return false;
    } finally {
      setLoadingAddresses(false);
    }
  };

  // Update state when selecting a saved address
  const selectAddress = (addressId) => {
    setSelectedAddressId(addressId);

    // Update shipping info form with the selected address
    const selectedAddress = savedAddresses.find(
      (addr) => addr.id === addressId
    );
    if (selectedAddress) {
      setShippingInfo({
        fullName: selectedAddress.full_name,
        phone: selectedAddress.phone,
        school_name: selectedAddress.school_name,
        nearest_busstand: selectedAddress.nearest_busstand,
        district: selectedAddress.district,
        zipCode: selectedAddress.zip_code,
        saveAddress: false,
        setAsDefault: false,
      });
    }
  };

  // Handle shipping form changes
  const handleShippingChange = (e) => {
    const { name, value, type, checked } = e.target;
    setShippingInfo({
      ...shippingInfo,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  // Validate shipping form
  const validateShippingForm = () => {
    const errors = {};
    if (!shippingInfo.fullName.trim()) {
      errors.fullName = "Full name is required";
    }
    if (!shippingInfo.phone.trim()) {
      errors.phone = "Phone number is required";
    }
    if (!shippingInfo.school_name.trim()) {
      errors.school_name = "School name is required";
    }
    if (!shippingInfo.nearest_busstand.trim()) {
      errors.nearest_busstand = "Nearest bus stand is required";
    }
    if (!shippingInfo.district.trim()) {
      errors.district = "District is required";
    }
    if (!shippingInfo.zipCode.trim()) {
      errors.zipCode = "PIN Code is required";
    }
    setShippingErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission for each step
  const handleContinue = async () => {
    if (step === 1) {
      let isValid = false;

      if (showNewAddressForm) {
        // If new address form is shown, validate and save it
        isValid = await saveNewAddress();
      } else if (selectedAddressId && selectedAddressId !== "") {
        // If using an existing address, just proceed
        isValid = true;
      } else {
        // No address selected or entered
        showNotification("Please select or add an address", "error");
        isValid = false;
      }

      if (isValid) {
        setStep(2);
        window.scrollTo(0, 0);
      }
    }
  };

  // Handle going back to previous step
  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
      window.scrollTo(0, 0);
    }
  };

  // Handle final order submission
  const handlePlaceOrder = async () => {
    // Prevent multiple clicks/submissions
    if (isProcessingPayment) {
      return;
    }

    // Add haptic feedback if available
    if ("vibrate" in navigator) {
      navigator.vibrate([40, 30, 100]);
    }

    try {
      // Validate that we have a valid address selected
      if (!selectedAddressId) {
        showNotification("Please select a delivery address", "error");
        setStep(1); // Go back to address selection
        return;
      }

      // Check if the selected address exists in saved addresses
      const addressExists = savedAddresses.some(
        (addr) => addr.id === selectedAddressId
      );
      if (!addressExists) {
        showNotification(
          "Selected address is invalid. Please choose another address.",
          "error"
        );
        setStep(1);
        return;
      } // Prepare order items
      const orderItems = cartItems.map((item) => ({
        product_id: parseInt(item.id) || 0,
        product_name: item.name,
        price: item.price,
        quantity: item.quantity,
        total_price: item.price * item.quantity,
      })); // Prepare order data
      const orderData = {
        items: orderItems,
        address_id: selectedAddressId,
        payment_method: paymentMethod,
        total_amount: pricing.total,
        delivery_fee: pricing.deliveryFee,
        discount_amount: pricing.discount,
        coupon_code: pricing.couponCode || null,
      };

      // Show loading overlay only for online payments
      if (
        paymentMethod === "online" &&
        paymentMethodSettings.online_payment_enabled
      ) {
        setIsProcessingPayment(true);
      }

      // Submit the order
      showNotification("Creating your order...", "info");

      const response = await window.ApiClient.createOrder(orderData);

      if (!response.success) {
        throw new Error(
          response.message || response.error || "Failed to create order"
        );
      }

      // Order created successfully
      const order = response.order;

      // Handle different payment methods
      if (paymentMethod === "online") {
        // For online payment, redirect to payment gateway
        showNotification("Redirecting to payment gateway...", "info");

        try {
          const paymentResponse = await window.ApiClient.initiatePayment(
            order.id
          );

          if (paymentResponse.success && paymentResponse.redirectUrl) {
            // Clear cart before redirecting to payment gateway
            if (typeof window !== "undefined" && window.CartUtils) {
              window.CartUtils.clearCart();
            }

            // Keep the loading overlay visible during redirect
            // The overlay will naturally disappear when the page unloads

            // Redirect to payment gateway
            window.location.href = paymentResponse.redirectUrl;
            return;
          } else {
            throw new Error(
              paymentResponse.message || "Payment initiation failed"
            );
          }
        } catch (paymentError) {
          console.error("Payment error:", paymentError);
          setIsProcessingPayment(false); // Hide loading overlay
          showNotification(
            "Payment initiation failed. Please try again.",
            "error"
          );
          return;
        }
      } else {
        // For cash on delivery, simply complete the order
        completeOrder(order);
      }
    } catch (error) {
      console.error("Order placement error:", error);
      // Hide loading overlay
      setIsProcessingPayment(false);

      // More descriptive error messages based on error type
      let errorMessage = error.message || "Failed to place order";

      if (errorMessage.includes("FOREIGN KEY constraint failed")) {
        errorMessage =
          "The selected address could not be used. Please select a different address.";
        setStep(1); // Go back to address selection
      } else if (errorMessage.includes("Authentication")) {
        errorMessage = "Your session has expired. Please log in again.";
        // Redirect to login after a short delay
        setTimeout(() => {
          window.location.href = "/login?redirect=/checkout";
        }, 2000);
      }

      showNotification(errorMessage, "error");
    }
  };

  // Complete the order after payment (or for COD)
  const completeOrder = (order) => {
    // Clear the cart
    if (typeof window !== "undefined" && window.CartUtils) {
      window.CartUtils.clearCart();
    }

    // Show success notification
    showNotification("Order placed successfully!", "success");

    // For COD orders, hide the loading overlay
    if (paymentMethod === "cash") {
      setIsProcessingPayment(false);
    }

    // Redirect to order confirmation page or orders list
    setTimeout(() => {
      window.location.href = `/?new_order=${order.id}`;
    }, 2000);
  };

  // Handle UPI Payment - Single button that opens native UPI app selector
  const handleUPIPayment = () => {
    const amount = pricing.total.toFixed(2);
    const merchantUPI = "9392333935@ybl"; // Replace with actual merchant UPI ID
    const merchantName = "Sreekar Publishers";
    const transactionNote = `Order payment - ${cartItems.length} items`;

    // Generate universal UPI link that will trigger the native app selector on mobile
    const upiLink = `upi://pay?pa=${merchantUPI}&pn=${encodeURIComponent(
      merchantName
    )}&am=${amount}&tn=${encodeURIComponent(transactionNote)}`;

    try {
      // Try to open the UPI app selector
      window.location.href = upiLink;

      // Show notification
      showNotification("Opening UPI apps...", "info");

      // Set a timeout to handle if no UPI apps are available
      setTimeout(() => {
        showNotification(
          "If no UPI app opened, please install a UPI app like PhonePe, Google Pay, or Paytm, or scan the QR code above.",
          "warning"
        );
      }, 3000);
    } catch (error) {
      console.error("Error opening UPI app:", error);
      showNotification(
        "Unable to open UPI apps. Please scan the QR code to pay.",
        "error"
      );
    }
  };

  // Generate QR Code for UPI payment
  const generateUPIQRCode = () => {
    const amount = pricing.total.toFixed(2);
    const merchantUPI = "9392333935@ybl"; // Replace with actual merchant UPI ID
    const merchantName = "Sreekar Publishers";
    const transactionNote = `Order payment - ${cartItems.length} items`;

    // UPI URL format for QR code
    const upiURL = `upi://pay?pa=${merchantUPI}&pn=${encodeURIComponent(
      merchantName
    )}&am=${amount}&tn=${encodeURIComponent(transactionNote)}`;

    // Generate QR code using a library or service
    // For now, we'll show a placeholder and generate via an API
    const qrContainer = document.getElementById("qr-code-container");
    if (qrContainer) {
      // Using QR Server API to generate QR code
      const qrCodeURL = `https://api.qrserver.com/v1/create-qr-code/?size=192x192&data=${encodeURIComponent(
        upiURL
      )}`;

      qrContainer.innerHTML = `
        <img 
          src="/images/sreekar-publishers-qr-code.jpg"
          alt="UPI Payment QR Code" 
          class="w-48 h-48 rounded-lg"
          // onError="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyIiBoZWlnaHQ9IjE5MiIgdmlld0JveD0iMCAwIDE5MiAxOTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjE5MiIgaGVpZ2h0PSIxOTIiIGZpbGw9IiNGM0Y0RjYiLz48cGF0aCBkPSJNOTYgNjRWMTI4SDEyOFY2NEg5NloiIGZpbGw9IiM2QjdCODAiLz48L3N2Zz4=';"
        />
      `;
    } else {
      //  setTimeout(generateUPIQRCode, 100);
    }
  };

  // Generate QR code when online payment is selected
  useEffect(() => {
    if (paymentMethod === "online" && pricing.total > 0) {
      // Small delay to ensure the container is rendered
      // setTimeout(generateUPIQRCode, 100);
    }
  }, [paymentMethod, pricing.total]);

  // Show notification with proper icon
  const showNotification = (message, type = "success") => {
    if (typeof window !== "undefined") {
      let toastContainer = document.querySelector(".toast-container");

      if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.className =
          "toast-container fixed bottom-24 sm:bottom-20 left-0 right-0 flex flex-col items-center z-50 pointer-events-none px-4";
        document.body.appendChild(toastContainer);
      }

      const toast = document.createElement("div");
      toast.className =
        "bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-lg opacity-0 transition-all duration-300 transform translate-y-4 mb-2 flex items-center max-w-md w-full justify-center";
      const icon = document.createElement("span");
      icon.className = "material-icons-round mr-2 text-[16px] md:text-[18px]";

      if (type === "error") {
        icon.textContent = "error";
      } else if (message.includes("success")) {
        icon.textContent = "check_circle";
      } else if (message.includes("Redirect")) {
        icon.textContent = "payments";
      } else {
        icon.textContent = "info";
      }

      toast.appendChild(icon);

      const textSpan = document.createElement("span");
      textSpan.textContent = message;
      toast.appendChild(textSpan);

      toastContainer.appendChild(toast);

      setTimeout(() => {
        toast.classList.remove("opacity-0", "translate-y-4");
        toast.classList.add("opacity-95");
      }, 10);

      setTimeout(() => {
        toast.classList.add("opacity-0", "translate-y-4");
        setTimeout(() => {
          toast.remove();
        }, 300);
      }, 2500);
    }
  };

  // Add effect to restore form data after login
  useEffect(() => {
    // Check if there's pending address data from before login
    if (
      typeof window !== "undefined" &&
      sessionStorage.getItem("pendingAddressData")
    ) {
      try {
        const savedData = JSON.parse(
          sessionStorage.getItem("pendingAddressData")
        );
        if (savedData) {
          setShippingInfo(savedData);
          setShowNewAddressForm(true);
          // Clear the saved data
          sessionStorage.removeItem("pendingAddressData");
        }
      } catch (e) {
        console.error("Failed to restore address data:", e);
      }
    }
  }, []);

  // Add new function to fetch user's phone number
  const fetchUserPhone = async () => {
    try {
      if (typeof window !== "undefined" && window.ApiClient) {
        const userData = window.ApiClient.getCurrentUser();
        if (userData && userData.phone) {
          setShippingInfo(prev => ({
            ...prev,
            phone: userData.phone
          }));
        }
      }
    } catch (error) {
      console.error("Error fetching user phone:", error);
    }
  };

  // Render progress steps with improved design
  const renderProgressSteps = () => (
    <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4 mb-6 overflow-hidden relative">
      {/* Decorative gradient background */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-orange-50 to-transparent rounded-bl-full -z-10"></div>

      <div className="flex items-center justify-between">
        <div className="flex flex-col items-center">
          <div
            className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${step >= 1
                ? "bg-[#5466F7] text-white shadow-md"
                : "bg-gray-100 text-gray-400"
              }`}
          >
            <span className="material-icons-round">location_on</span>
          </div>
          <span
            className={`text-xs mt-2 font-medium ${step >= 1 ? "text-gray-800" : "text-gray-400"
              }`}
          >
            Shipping
          </span>
        </div>
        <div
          className={`flex-1 h-1 mx-4 rounded-full transition-all duration-300 ${step >= 2 ? "bg-[#5466F7]" : "bg-gray-200"
            }`}
        ></div>

        <div className="flex flex-col items-center">
          <div
            className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${step >= 2
                ? "bg-[#5466F7] text-white shadow-md"
                : "bg-gray-100 text-gray-400"
              }`}
          >
            <span className="material-icons-round">fact_check</span>
          </div>
          <span
            className={`text-xs mt-2 font-medium ${step >= 2 ? "text-gray-800" : "text-gray-400"
              }`}
          >
            Review
          </span>
        </div>
      </div>
    </div>
  );
  // Render shipping/address selection with improved design
  const renderAddressSelection = () => (
    <div className="space-y-5">
      {/* Saved Addresses */}
      {savedAddresses.length > 0 && !showNewAddressForm && (
        <div className="space-y-3">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
            <span className="material-icons-round mr-3 text-[#5466F7]">
              location_on
            </span>
            Select Delivery Address
          </h2>
          {savedAddresses.map((address) => (
            <div
              key={address.id}
              className={`border rounded-xl p-4 cursor-pointer transition-all ${selectedAddressId === address.id
                  ? "border-[#5466F7] bg-blue-50"
                  : "border-gray-200 hover:border-blue-200"
                }`}
              onClick={() => selectAddress(address.id)}
            >
              <div className="flex items-start">
                <div
                  className={`w-5 h-5 rounded-full border flex-shrink-0 mt-1 ${selectedAddressId === address.id
                      ? "border-[#5466F7] border-2"
                      : "border-gray-300"
                    } flex items-center justify-center`}
                >
                  {selectedAddressId === address.id && (
                    <div className="w-2 h-2 rounded-full bg-[#5466F7]"></div>
                  )}
                </div>
                <div className="ml-3 flex-1">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium text-gray-800">
                        {address.full_name}
                      </h3>
                      {/* <p className="text-sm text-gray-600 mt-1">
                        {address.address}
                      </p> */}
                      <p className="text-sm text-gray-600">
                        {address.district} - {address.zip_code}
                      </p>
                      <p className="text-sm text-gray-600">
                        Nearest Bus Stand: {address.nearest_busstand}
                      </p>
                      <p className="text-sm text-gray-600">
                        School: {address.school_name}
                      </p>
                      <p className="text-sm text-gray-600">
                        Phone: {address.phone}
                      </p>
                      {address.instructions && (
                        <p className="text-sm text-gray-500 mt-1">
                          Instructions: {address.instructions}
                        </p>
                      )}
                    </div>
                    {address.is_default ? (
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        Default
                      </span>
                    ) : null}
                  </div>
                </div>
              </div>
            </div>
          ))}
          <button
            onClick={() => setShowNewAddressForm(true)}
            className="w-full py-3 border-2 border-dashed border-gray-300 rounded-xl text-gray-600 hover:border-[#5466F7] hover:text-[#5466F7] transition-all flex items-center justify-center"
          >
            <span className="material-icons-round mr-2">add</span>
            Add New Address
          </button>
        </div>
      )}

      {/* New Address Form */}
      {(showNewAddressForm || savedAddresses.length === 0) && (
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5">
          <h2 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
            <span className="material-icons-round mr-3 text-[#5466F7]">
              add_location
            </span>
            Add New Address
          </h2>
          <div className="space-y-4">
            <div>
              <label
                htmlFor="fullName"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Full Name
              </label>
              <input
                type="text"
                id="fullName"
                name="fullName"
                value={shippingInfo.fullName}
                onChange={handleShippingChange}
                className={`w-full px-4 py-3 rounded-lg border ${shippingErrors.fullName
                    ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:border-[#5466F7] focus:ring-[#5466F7]"
                  } focus:ring-2 focus:ring-opacity-50 transition-all`}
                placeholder="Enter your full name"
              />
              {shippingErrors.fullName && (
                <p className="mt-1 text-sm text-red-600">
                  {shippingErrors.fullName}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="phone"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Phone Number
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={shippingInfo.phone}
                onChange={handleShippingChange}
                className={`w-full px-4 py-3 rounded-lg border ${shippingErrors.phone
                    ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:border-[#5466F7] focus:ring-[#5466F7]"
                  } focus:ring-2 focus:ring-opacity-50 transition-all`}
                placeholder="Enter your phone number"
              />
              {shippingErrors.phone && (
                <p className="mt-1 text-sm text-red-600">
                  {shippingErrors.phone}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="school_name"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                School Name
              </label>
              <input
                type="text"
                id="school_name"
                name="school_name"
                value={shippingInfo.school_name}
                onChange={handleShippingChange}
                className={`w-full px-4 py-3 rounded-lg border ${shippingErrors.school_name
                    ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:border-[#5466F7] focus:ring-[#5466F7]"
                  } focus:ring-2 focus:ring-opacity-50 transition-all`}
                placeholder="Enter your school name"
              />
              {shippingErrors.school_name && (
                <p className="mt-1 text-sm text-red-600">
                  {shippingErrors.school_name}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="nearest_busstand"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Nearest Bus Stand
              </label>
              <input
                type="text"
                id="nearest_busstand"
                name="nearest_busstand"
                value={shippingInfo.nearest_busstand}
                onChange={handleShippingChange}
                className={`w-full px-4 py-3 rounded-lg border ${shippingErrors.nearest_busstand
                    ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:border-[#5466F7] focus:ring-[#5466F7]"
                  } focus:ring-2 focus:ring-opacity-50 transition-all`}
                placeholder="Enter nearest bus stand"
              />
              {shippingErrors.nearest_busstand && (
                <p className="mt-1 text-sm text-red-600">
                  {shippingErrors.nearest_busstand}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="district"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                District
              </label>
              <input
                type="text"
                id="district"
                name="district"
                value={shippingInfo.district}
                onChange={handleShippingChange}
                className={`w-full px-4 py-3 rounded-lg border ${shippingErrors.district
                    ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:border-[#5466F7] focus:ring-[#5466F7]"
                  } focus:ring-2 focus:ring-opacity-50 transition-all`}
                placeholder="Enter your district"
              />
              {shippingErrors.district && (
                <p className="mt-1 text-sm text-red-600">
                  {shippingErrors.district}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="zipCode"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                PIN Code
              </label>
              <input
                type="text"
                id="zipCode"
                name="zipCode"
                value={shippingInfo.zipCode}
                onChange={handleShippingChange}
                className={`w-full px-4 py-3 rounded-lg border ${shippingErrors.zipCode
                    ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:border-[#5466F7] focus:ring-[#5466F7]"
                  } focus:ring-2 focus:ring-opacity-50 transition-all`}
                placeholder="Enter your PIN Code"
              />
              {shippingErrors.zipCode && (
                <p className="mt-1 text-sm text-red-600">
                  {shippingErrors.zipCode}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // Render order review with improved design
  const renderOrderReview = () => (
    <div className="space-y-5">
      {/* Order Summary */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5 overflow-hidden relative">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-50 to-transparent rounded-bl-full -z-10"></div>
        <h2 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
          <span className="material-icons-round mr-3 text-[#5466F7]">
            shopping_bag
          </span>
          Order Summary
        </h2>
        <div className="divide-y divide-gray-100 overflow-hidden">
          {cartItems.map((item) => (
            <div className=" flex-1 flex flex-col">
              <div className="flex justify-between">
                <div>
                  <p className="font-medium text-gray-800 line-clamp-1">
                    {item.name}
                  </p>
                  {item.unit_type && item.unit_value && (
                    <div className="text-xs text-blue-600">
                      {item.unit_type === "quantity"
                        ? `${item.unit_value} ${item.unit_value > 1 ? "items" : "item"
                        }`
                        : `${item.unit_value}${item.unit_type}`}
                    </div>
                  )}
                  <p className="text-sm text-gray-500 mt-0.5">
                    {formatCurrency(item.price)} x {item.quantity}
                  </p>
                </div>
                <span className="font-semibold">
                  {formatCurrency(item.price * item.quantity)}
                </span>
              </div>
            </div>
          ))}{" "}
        </div>
      </div>

      {/* Shipping Details */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5 overflow-hidden relative">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-orange-50 to-transparent rounded-bl-full -z-10"></div>
        <h2 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
          <span className="material-icons-round mr-3 text-[#5466F7]">
            local_shipping
          </span>
          Delivery Address
        </h2>
        <div className="bg-gray-50 p-4 rounded-xl flex flex-col space-y-2">
          <div className="flex flex-col">
            <span className="w-24 text-gray-500 flex-shrink-0">Full Name:</span>
            <span className="font-medium text-gray-800">
              {shippingInfo.fullName}
            </span>
          </div>
          <div className="flex flex-col">
            <span className="w-24 text-gray-500 flex-shrink-0">Phone:</span>
            <span className="font-medium text-gray-800">
              {shippingInfo.phone}
            </span>
          </div>
          <div className="flex flex-col">
            <span className="w-24 text-gray-500 flex-shrink-0 self-start">
              Address:
            </span>
            <span className="font-medium text-gray-800">
              {shippingInfo.school_name} ,{shippingInfo.nearest_busstand} , {shippingInfo.district} - {shippingInfo.zipCode}
            </span>
          </div>
          {shippingInfo.instructions && (
            <div className="flex mt-1 pt-2 border-t border-gray-200 flex-col">
              <span className="w-24 text-gray-500 flex-shrink-0 self-start">
                Note:
              </span>
              <span className="text-gray-700">{shippingInfo.instructions}</span>
            </div>
          )}
        </div>
        <a
          href="#"
          onClick={(e) => {
            e.preventDefault();
            setStep(1);
          }}
          className="text-[#5466F7] text-sm font-medium mt-3 flex items-center"
        >
          <span className="material-icons-round text-sm mr-1">edit</span>
          Change address
        </a>
      </div>

      {/* Payment Method */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5 overflow-hidden relative">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-green-50 to-transparent rounded-bl-full -z-10"></div>
        <h2 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
          <span className="material-icons-round mr-3 text-[#5466F7]">
            payment
          </span>
          Payment Method
        </h2>

        {/* No payment methods available */}
        {!paymentMethodSettings.online_payment_enabled &&
          !paymentMethodSettings.cash_on_delivery_enabled && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800">
              <div className="flex items-center">
                <span className="material-icons-round mr-2 text-yellow-600">
                  warning
                </span>
                <p className="font-medium">Payment methods unavailable</p>
              </div>
              <p className="mt-1 text-sm">
                All payment methods are currently unavailable. Please try again
                later.
              </p>
            </div>
          )}

        {/* Cash on Delivery unavailable due to minimum order amount */}
        {paymentMethodSettings.online_payment_enabled &&
          !paymentMethodSettings.cash_on_delivery_enabled &&
          pricing.total < 1200 && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg text-blue-800 mb-4">
              <div className="flex items-center">
                <span className="material-icons-round mr-2 text-blue-600">
                  info
                </span>
                <p className="font-medium">Cash on Delivery not available</p>
              </div>
              <p className="mt-1 text-sm">
                Cash on Delivery is available for orders of ₹1200 or more. Add ₹{(1200 - pricing.total).toFixed(0)} more to enable COD.
              </p>
            </div>
          )}

        {/* At least one payment method is available */}
        {(paymentMethodSettings.online_payment_enabled ||
          paymentMethodSettings.cash_on_delivery_enabled) && (
            <div className="flex flex-col space-y-3">
              {/* Online Payment Option */}
              {paymentMethodSettings.online_payment_enabled && (
                <div
                  className={`border rounded-xl transition-all ${paymentMethod === "online"
                      ? "border-[#5466F7] bg-blue-50 shadow"
                      : "border-gray-200 hover:border-blue-200"
                    }`}
                >
                  <div
                    className="p-5 cursor-pointer"
                    onClick={() => setPaymentMethod("online")}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div
                          className={`flex-shrink-0 w-10 h-10 rounded-full ${paymentMethod === "online"
                              ? "bg-[#5466F7] text-white"
                              : "bg-gray-100 text-gray-400"
                            } flex items-center justify-center`}
                        >
                          <span className="material-icons-round">
                            credit_card
                          </span>
                        </div>
                        <div className="ml-4">
                          <h3 className="font-semibold text-gray-800">
                            Online Payment
                          </h3>
                          <p className="text-xs text-gray-500 mt-1">
                            Pay securely via UPI/Card/Net Banking
                          </p>
                        </div>
                      </div>
                      <div
                        className={`w-5 h-5 rounded-full border ${paymentMethod === "online"
                            ? "border-[#5466F7] border-2"
                            : "border-gray-300"
                          } flex items-center justify-center`}
                      >
                        {paymentMethod === "online" && (
                          <div className="w-2 h-2 rounded-full bg-[#5466F7]"></div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* UPI Payment Section - Show when online payment is selected */}
                  {paymentMethod === "online" && (
                    <div className="border-t border-blue-100 p-5 bg-gradient-to-br from-blue-50 to-white">
                      <div className="space-y-4">
                        {/* QR Code Section */}
                        <div className="bg-white rounded-xl p-4 border border-blue-100 shadow-sm">
                          <div className="text-center">
                            <h4 className="font-semibold text-gray-800 mb-2 flex items-center justify-center">
                              <span className="material-icons-round text-blue-600 mr-2">
                                qr_code
                              </span>
                              Scan QR Code to Pay
                            </h4>
                            <p className="text-xs text-gray-500 mb-4">
                              Use any UPI app to scan and pay
                            </p>

                            <div className="flex justify-center mb-4">
                              <div className="bg-white p-4 rounded-xl border border-gray-200 shadow-sm">
                                <div
                                  id="qr-code-container"
                                  className="w-48 h-48 bg-gray-50 border border-gray-200 rounded-lg flex items-center justify-center"
                                >
                                  {/* <div className="text-center">
                                  <span className="material-icons-round text-gray-400 text-4xl mb-2">
                                    qr_code_scanner
                                  </span>
                                  <p className="text-sm text-gray-500">
                                    QR Code will appear here
                                  </p>
                                </div> */}
                                  <img
                                    src="/images/sreekar-publishers-qr-code.jpeg"
                                    alt="UPI Payment QR Code"
                                    class="w-48 h-48 rounded-lg"
                                  // onError="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyIiBoZWlnaHQ9IjE5MiIgdmlld0JveD0iMCAwIDE5MiAxOTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjE5MiIgaGVpZ2h0PSIxOTIiIGZpbGw9IiNGM0Y0RjYiLz48cGF0aCBkPSJNOTYgNjRWMTI4SDEyOFY2NEg5NloiIGZpbGw9IiM2QjdCODAiLz48L3N2Zz4=';"
                                  />
                                </div>
                              </div>
                            </div>

                            <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg border border-blue-100">
                              <p className="font-medium text-blue-800 mb-1">
                                Payment Amount: ₹{pricing.total.toFixed(2)}
                              </p>
                              <p className="text-xs text-blue-600">
                                UPI ID: sreekar9935@fbl
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* UPI Payment Button */}
                        <div className="bg-white rounded-xl p-4 border border-blue-100 shadow-sm">
                          <button
                            onClick={() => handleUPIPayment()}
                            className="w-full flex items-center justify-center p-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all shadow-md hover:shadow-lg transform hover:scale-[1.02]"
                          >
                            <div className="flex items-center">
                              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                                <span className="material-icons-round text-green-600">
                                  smartphone
                                </span>
                              </div>
                              <div className="text-left">
                                <p className="text-base font-semibold text-white">
                                  Pay via UPI
                                </p>
                                {/* <p className="text-sm text-blue-100">Choose from installed apps</p> */}
                              </div>
                            </div>
                            <span className="material-icons-round text-white ml-auto">
                              chevron_right
                            </span>
                          </button>
                          <p className="text-xs text-gray-500 mt-3 text-center">
                            Clicking this button will show all UPI apps installed
                            on your device
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Cash on Delivery Option */}
              {paymentMethodSettings.cash_on_delivery_enabled && (
                <div
                  className={`border rounded-xl p-5 transition-all cursor-pointer ${paymentMethod === "cash"
                      ? "border-[#5466F7] bg-blue-50 shadow"
                      : "border-gray-200 hover:border-blue-200"
                    }`}
                  onClick={() => setPaymentMethod("cash")}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div
                        className={`flex-shrink-0 w-10 h-10 rounded-full ${paymentMethod === "cash"
                            ? "bg-[#5466F7] text-white"
                            : "bg-gray-100 text-gray-400"
                          } flex items-center justify-center`}
                      >
                        <span className="material-icons-round">payments</span>
                      </div>
                      <div className="ml-4">
                        <h3 className="font-semibold text-gray-800">
                          Cash on Delivery
                        </h3>
                        <p className="text-xs text-gray-500 mt-1">
                          Pay when order arrives
                        </p>
                      </div>
                    </div>
                    <div
                      className={`w-5 h-5 rounded-full border ${paymentMethod === "cash"
                          ? "border-[#5466F7] border-2"
                          : "border-gray-300"
                        } flex items-center justify-center`}
                    >
                      {paymentMethod === "cash" && (
                        <div className="w-2 h-2 rounded-full bg-[#5466F7]"></div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
      </div>

      {/* Price Details */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5 overflow-hidden relative">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-50 to-transparent rounded-bl-full -z-10"></div>
        <h2 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
          <span className="material-icons-round mr-3 text-[#5466F7]">
            receipt_long
          </span>
          Price Details
        </h2>
        <div className="space-y-3">
          <div className="flex justify-between py-1.5">
            <span className="text-gray-500">Subtotal</span>
            <span className="font-medium text-gray-800">
              ₹ {pricing.subtotal.toFixed(2)}
            </span>
          </div>
          <div className="flex justify-between py-1.5">
            <span className="text-gray-500">
              {pricing.deliveryFee > 0 ? "Transport Charge" : "Transport Charge"}
            </span>
            <span className="font-medium">
              {pricing.deliveryFee > 0 ? (
                <span className="text-gray-800">
                  ₹ {pricing.deliveryFee.toFixed(0)}
                </span>
              ) : (
                <span className="text-green-600">Free</span>
              )}
            </span>
          </div>
          {pricing.discount > 0 && (
            <div className="flex justify-between text-green-600 py-1.5">
              <span className="flex items-center">
                <span className="material-icons-round text-sm mr-1.5">
                  discount
                </span>
                <span>
                  {couponInfo ? `Discount (${couponInfo.code})` : "Discount"}
                </span>
              </span>
              <span className="font-medium">
                -₹ {pricing.discount.toFixed(2)}
              </span>
            </div>
          )}
          <div className="border-t border-gray-100 pt-3 mt-3">
            <div className="flex justify-between items-center">
              <span className="font-semibold text-gray-800">Total</span>
              <div>
                <span className="text-2xl font-bold text-[#5466F7]">
                  ₹ {pricing.total.toFixed(2)}
                </span>
              </div>
            </div>

            {/* Free Delivery Notification */}
            {pricing.isFreeDelivery && (
              <div className="mt-4 p-2.5 bg-green-50 border border-green-100 rounded-lg text-green-700 text-sm flex items-center">
                <span className="material-icons-round text-sm mr-1.5">
                  local_shipping
                </span>
                <span>Free transport - order above ₹1200!</span>
              </div>
            )}

            {/* Free Delivery Threshold Notification */}
            {!pricing.isFreeDelivery && pricing.freeDeliveryThreshold > 0 && (
              <div className="mt-4 p-2.5 bg-blue-50 border border-blue-100 rounded-lg text-blue-700 text-sm flex items-center">
                <span className="material-icons-round text-sm mr-1.5">
                  info
                </span>
                <span>
                  Add ₹
                  {(pricing.freeDeliveryThreshold - pricing.subtotal).toFixed(
                    0
                  )}{" "}
                  more to get free transport!
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="pb-24 sm:pb-20 max-w-xl mx-auto">
      {/* Payment Processing Overlay - Only shown for online payments */}
      <LoadingOverlay
        isVisible={isProcessingPayment && paymentMethod === "online"}
        message="Processing your payment..."
        spinnerSize="lg"
        spinnerColor="blue"
        icon="payments"
      />

      {/* Initial Loading State */}
      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-20">
          <div className="w-16 h-16 border-4 border-gray-100 border-t-[#5466F7] border-b-[#5466F7] rounded-full animate-spin mb-5"></div>
          <p className="text-gray-600 font-medium">
            Loading your order details...
          </p>
        </div>
      ) : (
        <>
          {/* Empty Cart State */}
          {cartItems.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
              <div className="w-24 h-24 bg-blue-50 rounded-full flex items-center justify-center mb-6 border border-blue-100 shadow-inner">
                <span className="material-icons-round text-[#5466F7] text-[40px]">
                  shopping_cart
                </span>
              </div>
              <h2 className="text-xl font-semibold text-gray-800 mb-3">
                Your cart is empty
              </h2>
              <p className="text-gray-500 mb-8 max-w-xs">
                Looks like you haven't added any items to your cart yet. Start
                browsing our educational materials!
              </p>
              <a
                href="/"
                className="bg-[#5466F7] text-white px-8 py-3.5 rounded-xl font-medium hover:bg-[#4555e2] transition-all flex items-center justify-center shadow-sm hover:shadow"
              >
                <span className="material-icons-round mr-2">school</span>
                Browse Materials
              </a>
            </div>
          ) : (
            <div className="px-4">
              {renderProgressSteps()}
              {step === 1 && renderAddressSelection()}
              {step === 2 && renderOrderReview()}

              {/* Action Buttons */}
              <div className="flex flex-col gap-4 mt-6 mb-10">
                {step > 1 && (
                  <button
                    onClick={handleBack}
                    className="flex-1 py-4 border border-gray-200 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all flex items-center justify-center shadow-sm"
                  >
                    <span className="material-icons-round mr-2">
                      arrow_back
                    </span>
                    Back
                  </button>
                )}

                {step < 2 ? (
                  <button
                    onClick={handleContinue}
                    className="flex-1 py-4 bg-[#5466F7] text-white rounded-xl font-medium hover:bg-[#4555e2] transition-all flex items-center justify-center shadow-sm hover:shadow"
                  >
                    Continue
                    <span className="material-icons-round ml-2">
                      arrow_forward
                    </span>
                  </button>
                ) : (
                  <button
                    onClick={handlePlaceOrder}
                    disabled={isProcessingPayment}
                    className={`flex-1 py-4 rounded-xl font-semibold transition-all flex items-center justify-center shadow-md ${isProcessingPayment
                        ? "bg-gray-400 text-white cursor-not-allowed"
                        : "bg-[#5466F7] text-white hover:bg-[#4555e2] hover:shadow-lg"
                      }`}
                  >
                    <span className="material-icons-round mr-2">
                      {paymentMethod === "online" ? "payments" : "check_circle"}
                    </span>
                    {isProcessingPayment
                      ? "Processing..."
                      : paymentMethod === "online"
                        ? "Place Order"
                        : "Place Order"}
                  </button>
                )}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
