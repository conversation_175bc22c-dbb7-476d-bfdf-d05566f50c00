import type { APIRoute } from 'astro';
import { authMiddleware } from '../../../middleware/auth';
import type { Order } from '../../../db/database';
import {
  getDeliveryBoyById,
  getDeliveryBoyOrders,
  updateOrderStatus,
  assignDeliveryBoyToOrder
} from '../../../db/database';

export const prerender = false;

/**
 * Get orders for a delivery boy
 */
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Authenticate user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    const { user } = authResult as any;

    // Check if the user is a delivery boy
    // const deliveryBoy = await getDeliveryBoyById(locals.runtime.env, user.id);

    // if (!deliveryBoy) {
    //   return new Response(JSON.stringify({ error: "Not authorized as delivery boy" }), {
    //     status: 403,
    //     headers: { 'Content-Type': 'application/json' }
    //   });
    // }

    // Parse query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get('status');

    // Get orders assigned to this delivery boy
    const orders = await getDeliveryBoyOrders(locals.runtime.env, user.id, status || undefined);

    return new Response(JSON.stringify({
      success: true,
      orders
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching delivery orders:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch delivery orders' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/**
 * Update order status or assign delivery boy to order
 */
export const PATCH: APIRoute = async ({ request, locals }) => {
  try {
    // Authenticate user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    const { user } = authResult as any;

    // Check if the user is a delivery boy
    const deliveryBoy = await getDeliveryBoyById(locals.runtime.env, user.id);

    if (!deliveryBoy) {
      return new Response(JSON.stringify({ error: "Not authorized as delivery boy" }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get request data
    const data = await request.json() as { orderId: number; action: string; status?: string };
    const { orderId, action, status } = data;

    if (!orderId || !action) {
      return new Response(JSON.stringify({ error: "Order ID and action are required" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    let success = false;

    // Handle different actions
    switch (action) {
      case 'update_status':
        if (!status) {
          return new Response(JSON.stringify({ error: "Status is required" }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        // Only allow delivery boy to update to 'out_for_delivery' or 'delivered'
        if (status !== 'out_for_delivery' && status !== 'delivered') {
          return new Response(JSON.stringify({ error: "Invalid status for delivery boy" }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        success = await updateOrderStatus(locals.runtime.env, orderId, status);
        break;

      case 'assign_self':
        success = await assignDeliveryBoyToOrder(locals.runtime.env, orderId, user.id);
        break;

      default:
        return new Response(JSON.stringify({ error: "Invalid action" }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
    }

    if (!success) {
      return new Response(JSON.stringify({ error: `Failed to ${action.replace('_', ' ')}` }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: `Successfully ${action.replace('_', ' ')}`
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error updating delivery order:', error);
    return new Response(JSON.stringify({ error: 'Failed to update delivery order' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
