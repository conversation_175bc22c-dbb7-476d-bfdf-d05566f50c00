import { getUserAddresses, updateUserAddress, deleteUserAddress } from '../../../db/database';
import { authMiddleware } from '../../../middleware/auth';

export const prerender = false;

/**
 * API endpoint to handle individual user addresses
 */
export async function PUT({ locals, request, params }) {
  // Check authentication first
  const authResult = await authMiddleware({ request });
  if (authResult instanceof Response) {
    return authResult; // Return 401 if not authenticated
  }
  
  const user = authResult.user;
  const addressId = params.id;
  
  if (!addressId) {
    return new Response(JSON.stringify({ error: "Address ID is required" }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  }
  
  if (!locals.runtime || !locals.runtime.env) {
    return new Response(JSON.stringify({ error: "Runtime environment not available" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    const data = await request.json();
    
    // Validate required fields
    const requiredFields = ['full_name', 'phone', 'school_name', 'nearest_busstand', 'district', 'zip_code'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return new Response(JSON.stringify({ error: `${field} is required` }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // Create address object
    const addressData = {
      id: Number(addressId),
      user_id: user.id,
      full_name: data.full_name,
      phone: data.phone,
      school_name: data.school_name,
      nearest_busstand: data.nearest_busstand,
      district: data.district,
      zip_code: data.zip_code,
      is_default: data.is_default === true || data.is_default === 1
    };

    const updatedAddress = await updateUserAddress(locals.runtime.env, addressData);
    
    if (!updatedAddress) {
      return new Response(JSON.stringify({ error: "Failed to update address" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({ address: updatedAddress }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error("Error in addresses API:", error);
    return new Response(JSON.stringify({ error: "Failed to update address" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function DELETE({ locals, request, params }) {
  // Check authentication first
  const authResult = await authMiddleware({ request });
  if (authResult instanceof Response) {
    return authResult; // Return 401 if not authenticated
  }
  
  const user = authResult.user;
  const addressId = params.id;
  
  if (!addressId) {
    return new Response(JSON.stringify({ error: "Address ID is required" }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  }
  
  if (!locals.runtime || !locals.runtime.env) {
    return new Response(JSON.stringify({ error: "Runtime environment not available" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    const result = await deleteUserAddress(locals.runtime.env, Number(addressId), user.id);
    
    if (!result) {
      return new Response(JSON.stringify({ error: "Failed to delete address" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error("Error in addresses API:", error);
    return new Response(JSON.stringify({ error: "Failed to delete address" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
