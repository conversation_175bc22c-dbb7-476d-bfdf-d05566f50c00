import type { APIRoute } from 'astro';
import { getOrderById, createPaymentTransaction, getOrderPaymentTransactions } from '../../../db/database';
import { authMiddleware } from '../../../middleware/auth';
import axios from 'axios';

export const prerender = false;

// PhonePe v2 API configuration (use environment variables)
const getPhonePeConfig = (env: any) => ({
  // v2 API endpoints
  PHONEPE_API_URL:  "https://api.phonepe.com/apis/pg",
  PHONEPE_AUTH_URL:   "https://api.phonepe.com/apis/identity-manager/v1/oauth/token",
  MERCHANT_ID:   "M23LZOY7DEJCI", // Your production merchant ID
  // v2 API uses client credentials for OAuth
  CLIENT_ID:   "SU2507051851592568452462",
  CLIENT_SECRET:   "d1c16b8b-8638-45ff-aa09-f93d7f693720",
  // Keep v1 config for backward compatibility if needed
  SALT_KEY:   "d1c16b8b-8638-45ff-aa09-f93d7f693720",
  SALT_INDEX:  "1"
});

/**
 * Get access token for PhonePe v2 API
 */
async function getPhonePeAccessToken(config: any): Promise<string> {
  try {
    const authPayload = {
      grant_type: "client_credentials",
      client_id: config.CLIENT_ID,
      client_secret: config.CLIENT_SECRET
    };

    const response = await axios.post(`${config.PHONEPE_AUTH_URL}`, authPayload, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      }
    });

    if (response.data && response.data.access_token) {
      return response.data.access_token;
    } else {
      throw new Error('No access token received from PhonePe auth API');
    }
  } catch (error: any) {
    console.error('PhonePe authorization failed:', error.response?.data || error.message);
    throw new Error(`Failed to get PhonePe access token: ${error.response?.data?.message || error.message}`);
  }
}

/**
 * Initiate a payment for an order using PhonePe v2 API
 */
export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Get PhonePe configuration
    const config = getPhonePeConfig(locals.runtime.env);

    // Authenticate the user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return authentication error
    }

    const user = (authResult as any).user;
    
    // Parse request body
    const { order_id } = await request.json() as { order_id: number };
    
    if (!order_id) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "Order ID is required" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify order exists and belongs to the user
    const order = await getOrderById(locals.runtime.env, order_id, user.id);
    
    if (!order) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "Order not found" 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify payment method
    if (order.payment_method !== 'online') {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "This order doesn't require online payment" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify payment status (should be pending)
    if (order.payment_status !== 'pending') {
      return new Response(JSON.stringify({ 
        success: false, 
        message: `Payment already ${order.payment_status}` 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Check for existing transactions
    const transactions = await getOrderPaymentTransactions(locals.runtime.env, order.id);
    const pendingTransaction = transactions.find(tx => 
      tx.status === 'initiated' || tx.status === 'pending');
    
    if (pendingTransaction) {
      // Return existing transaction details
      try {
        const gatewayResponse = JSON.parse(pendingTransaction.gateway_response || '{}');
        return new Response(JSON.stringify({ 
          success: true,
          transaction: pendingTransaction,
          redirectUrl: gatewayResponse.data?.instrumentResponse?.redirectInfo?.url || null,
          message: "Payment already initiated"
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (e) {
        // Handle parse error
        console.error('Error parsing gateway response:', e);
      }
    }
    
    // Generate a unique merchant transaction ID (must be < 35 characters)
    const merchantTxnId = `MT${Date.now()}${Math.floor(Math.random() * 100)}`;
    
    // Get phone number from address
    let phoneNumber = '';
    if (order.address && order.address.phone) {
      phoneNumber = order.address.phone.replace(/[^0-9]/g, '');
    }
    
    // Validate amount (PhonePe requires minimum 100 paise = ₹1)
    const amountInPaise = Math.round(order.total_amount * 100);
    if (amountInPaise < 100) {
      return new Response(JSON.stringify({
        success: false,
        message: "Amount must be at least ₹1"
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get access token for v2 API
    let accessToken: string;
    try {
      accessToken = await getPhonePeAccessToken(config);
    } catch (error: any) {
      console.error('Failed to get PhonePe access token:', error);
      return new Response(JSON.stringify({
        success: false,
        message: "Payment gateway authentication failed",
        error: error.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Create PhonePe v2 API request payload
    const phonepePayload = {
      merchantOrderId: merchantTxnId, // v2 uses merchantOrderId instead of merchantTransactionId
      amount: amountInPaise,
      // expireAfter: 1200, // 20 minutes expiry (optional, defaults to 1200)
      metaInfo: {
        udf1: `${order.id}`,
        udf2: user.name || "Customer",
        udf3: phoneNumber || "",
        udf4: `items_${order.items?.length || 0}`,
        udf5: "srikar_publications"
      },
      paymentFlow: {
        type: "PG_CHECKOUT",
        message: `Payment for Order #${order.id}`,
        merchantUrls: {
          redirectUrl: `${new URL(request.url).origin}/checkout/status?transactionId=${merchantTxnId}`
        }
      }
    };

    // Log payload for debugging (remove in production)
    console.log('PhonePe v2 Payload:', JSON.stringify(phonepePayload, null, 2));
    console.log('Config being used:', {
      MERCHANT_ID: config.MERCHANT_ID,
      CLIENT_ID: config.CLIENT_ID,
      API_URL: config.PHONEPE_API_URL,
      ACCESS_TOKEN: accessToken ? 'Present' : 'Missing'
    });

    // Make API call to PhonePe v2 using axios
    const options = {
      method: 'POST',
      url: `${config.PHONEPE_API_URL}/checkout/v2/pay`,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `O-Bearer ${accessToken}`
      },
      data: phonepePayload // v2 API uses direct JSON payload, no base64 encoding
    };

    console.log('Making PhonePe API request with options:', JSON.stringify(options, null, 2));

    let phonepeResponse: any;

    try {
      const response = await axios.request(options);

      // Log response status and headers for debugging
      console.log('PhonePe API Response Status:', response.status);
      console.log('PhonePe API Response Headers:', response.headers);

      phonepeResponse = response.data;

      // Log full response for debugging
      console.log('PhonePe API Response:', JSON.stringify(phonepeResponse, null, 2));

    } catch (error: any) {
      console.error('PhonePe API request failed:', error);

      if (error.response) {
        // The request was made and the server responded with a status code
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);

        return new Response(JSON.stringify({
          success: false,
          message: error.response.data?.message || "Payment gateway error",
          code: error.response.data?.code || "API_ERROR",
          debug: {
            status: error.response.status,
            merchantId: config.MERCHANT_ID,
            payloadSize: JSON.stringify(phonepePayload).length
          }
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (error.request) {
        // The request was made but no response was received
        console.error('No response received:', error.request);
        return new Response(JSON.stringify({
          success: false,
          message: "No response from payment gateway",
          code: "NO_RESPONSE"
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      } else {
        // Something happened in setting up the request
        console.error('Request setup error:', error.message);
        return new Response(JSON.stringify({
          success: false,
          message: "Failed to setup payment request",
          code: "REQUEST_ERROR"
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // v2 API response structure is different - check for errors
    if (phonepeResponse.code && phonepeResponse.code !== 'SUCCESS') {
      console.error('PhonePe v2 API error:', phonepeResponse);
      return new Response(JSON.stringify({
        success: false,
        message: phonepeResponse.message || "Payment gateway error",
        code: phonepeResponse.code || "UNKNOWN_ERROR",
        debug: {
          merchantId: config.MERCHANT_ID,
          apiUrl: config.PHONEPE_API_URL,
          payloadSize: JSON.stringify(phonepePayload).length
        }
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Store transaction record in our database
    const transaction = await createPaymentTransaction(locals.runtime.env, {
      order_id: order.id,
      transaction_id: merchantTxnId,
      payment_method: 'phonepe',
      amount: order.total_amount,
      status: 'initiated',
      gateway_response: JSON.stringify(phonepeResponse)
    });
    
    if (!transaction) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "Failed to store transaction" 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Return success with redirect URL for the payment page (v2 API response structure)
    return new Response(JSON.stringify({
      success: true,
      transaction: transaction,
      redirectUrl: phonepeResponse.redirectUrl, // v2 API returns redirectUrl directly
      orderId: phonepeResponse.orderId, // v2 API returns orderId
      state: phonepeResponse.state, // v2 API returns state
      expireAt: phonepeResponse.expireAt, // v2 API returns expireAt
      message: "Payment initiated successfully"
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error initiating payment:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      message: "Failed to initiate payment" 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};