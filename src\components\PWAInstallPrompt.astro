<div id="pwa-install-prompt" class="pwa-prompt" style="display: none;">
  <div class="pwa-prompt-content">
    <div class="pwa-prompt-header">
      <img src="/images/icons/icon-96x96.png" alt="App Icon" class="pwa-prompt-icon">
      <h3>Install Sreekar Publishers App</h3>
    </div>
    <p>Install our app for a better experience and offline access.</p>
    <div class="pwa-prompt-buttons">
      <button id="pwa-install-button" class="pwa-install-button">Install</button>
      <button id="pwa-dismiss-button" class="pwa-dismiss-button">Not Now</button>
    </div>
  </div>
</div>

<style>
  .pwa-prompt {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-width: 90%;
    width: 400px;
  }

  .pwa-prompt-content {
    text-align: center;
  }

  .pwa-prompt-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
  }

  .pwa-prompt-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
  }

  .pwa-prompt-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 16px;
  }

  .pwa-install-button {
    background: #FF6B35;
    color: white;
    border: none;
    padding: 8px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
  }

  .pwa-dismiss-button {
    background: #f0f0f0;
    color: #333;
    border: none;
    padding: 8px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
  }
</style>

<script>
  interface BeforeInstallPromptEvent extends Event {
    prompt: () => Promise<void>;
    userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
  }

  let deferredPrompt: BeforeInstallPromptEvent | null = null;
  const promptElement = document.getElementById('pwa-install-prompt');
  const installButton = document.getElementById('pwa-install-button');
  const dismissButton = document.getElementById('pwa-dismiss-button');

  // Debug function
  function debug(message: string) {
    console.log(`[PWA Install] ${message}`);
  }

  // Check if we should show the prompt (30 minutes cooldown)
  function shouldShowPrompt(): boolean {
    const lastDismissed = localStorage.getItem('pwa-prompt-dismissed');
    if (!lastDismissed) {
      return true;
    }
    
    const dismissedTime = parseInt(lastDismissed);
    const now = Date.now();
    const thirtyMinutes = 30 * 60 * 1000; // 30 minutes in milliseconds
    
    return (now - dismissedTime) >= thirtyMinutes;
  }

  // Check if the app is already installed
  if (window.matchMedia('(display-mode: standalone)').matches) {
    debug('App is already installed');
  }

  if (!promptElement || !installButton || !dismissButton) {
    console.error('PWA prompt elements not found');
  } else {
    // Listen for the beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (e) => {
      debug('beforeinstallprompt event fired');
      e.preventDefault();
      deferredPrompt = e as BeforeInstallPromptEvent;
      
      // Show the prompt after a delay only if we should show it
      setTimeout(() => {
        if (shouldShowPrompt()) {
          debug('Showing install prompt');
          promptElement.style.display = 'block';
        } else {
          debug('Prompt dismissed recently, waiting for cooldown period');
        }
      }, 3000);
    });

    // Listen for successful installation
    window.addEventListener('appinstalled', (e) => {
      debug('App was installed');
      promptElement.style.display = 'none';
      deferredPrompt = null;
    });

    installButton.addEventListener('click', async () => {
      debug('Install button clicked');
      if (!deferredPrompt) {
        debug('No deferred prompt available');
        return;
      }
      
      try {
        debug('Prompting user to install');
        deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;
        debug(`User choice: ${outcome}`);
        
        if (outcome === 'accepted') {
          debug('User accepted the install prompt');
        } else {
          debug('User dismissed the install prompt');
        }
      } catch (error) {
        console.error('Error during installation:', error);
      }
      
      deferredPrompt = null;
      promptElement.style.display = 'none';
    });

    dismissButton.addEventListener('click', () => {
      debug('Dismiss button clicked');
      // Store the dismissal timestamp
      localStorage.setItem('pwa-prompt-dismissed', Date.now().toString());
      promptElement.style.display = 'none';
    });
  }
</script> 