---
import MainLayout from "../layouts/MainLayout.astro";
import OrderHistory from "../components/order/OrderHistory.jsx";
export const prerender = false;
const tab = Astro.url.searchParams.get("tab") || "all";
console.log("🚀 ~ tab:", tab)
---

<MainLayout
  title="Order History - Sreekar Publishers"
  headerTitle="My Orders"
  showHeader={true}
  showBackButton={false}
>
  <OrderHistory client:load tab={tab} />
</MainLayout>

<script>
  // Check authentication status on page load
  document.addEventListener("DOMContentLoaded", () => {
    // Check if user is authenticated
    const checkAuth = () => {
      try {
        if (typeof window.ApiClient !== "undefined") {
          if (!window.ApiClient.isAuthenticated()) {
            // User is not authenticated, redirect to login
            const currentPath =
              window.location.pathname + window.location.search;
            window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
            return false;
          } else {
            // Check if we should set a specific tab
            const urlParams = new URLSearchParams(window.location.search);
            const tab = urlParams.get("tab");

            if (tab) {
              // Use a custom event to communicate with the React component
              const event = new CustomEvent("setActiveTab", {
                detail: { tab },
              });
              document.dispatchEvent(event);              // Clean URL for better UX
              const url = new URL(window.location.href);
              url.searchParams.delete("tab");
              history.replaceState({}, document.title, url.toString());
            }
            return true;
          }
        } else {
          // ApiClient not available, try again after a short delay
          setTimeout(checkAuth, 500);
          return false;
        }
      } catch (error) {
        console.error("Authentication check failed:", error);
        // On error, redirect to login as a fallback
        const currentPath = window.location.pathname + window.location.search;
        window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
        return false;
      }
    };

    // Initial check
    checkAuth();    // Set up an event listener for logout events
    window.addEventListener("auth-status-changed", function (e) {
      // Check authentication again
      const customEvent = e as CustomEvent;
      if (customEvent.detail && customEvent.detail.action === "logout") {
        // If logout action, redirect immediately
        window.location.href = "/login?redirect=/orders";
      } else {
        checkAuth();
      }
    });
  });
</script>
