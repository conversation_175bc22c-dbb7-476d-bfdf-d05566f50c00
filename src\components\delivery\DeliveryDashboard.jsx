import React, { useState, useEffect } from 'react';
import LoadingSpinner from '../common/LoadingSpinner';
import ErrorAlert from '../common/ErrorAlert';
import SuccessAlert from '../common/SuccessAlert';

export default function DeliveryDashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [deliveryBoy, setDeliveryBoy] = useState(null);
  const [orders, setOrders] = useState([]);
  const [selectedTab, setSelectedTab] = useState('processing'); // Order status: 'processing', 'out_for_delivery', 'delivered'
  const [statusCounts, setStatusCounts] = useState({
    processing: 0,
    out_for_delivery: 0,
    delivered: 0
  });
  const [isUpdatingOrder, setIsUpdatingOrder] = useState(false);

  // Fetch delivery boy data on component mount
  useEffect(() => {
    fetchDeliveryBoyData();
  }, []);

  // Fetch orders when tab changes
  useEffect(() => {
    fetchOrders(selectedTab);
  }, [selectedTab]);

  // Fetch status counts when component mounts
  useEffect(() => {
    fetchStatusCounts();
  }, []);

  // Fetch delivery boy data
  const fetchDeliveryBoyData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch only delivery boy data
      const data = await window.ApiClient.getDeliveryProfile();

      if (data.success) {
        setDeliveryBoy(data.deliveryBoy || null);
      } else {
        setError(data.error || 'Failed to load delivery profile data');
      }
    } catch (err) {
      console.error('Error fetching delivery profile data:', err);
      setError('Failed to load delivery profile data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch orders for a specific status
  const fetchOrders = async (status) => {
    try {
      setIsLoading(true);
      setError(null);

      const data = await window.ApiClient.getDeliveryOrders(status);

      if (data.success) {
        setOrders(data.orders || []);
      } else {
        setError(data.error || 'Failed to load orders');
      }
    } catch (err) {
      console.error('Error fetching orders:', err);
      setError('Failed to load orders. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch status counts for all orders
  const fetchStatusCounts = async () => {
    try {
      const data = await window.ApiClient.getDeliveryOrderCounts();

      if (data.success) {
        setStatusCounts(data.counts || {
          processing: 0,
          out_for_delivery: 0,
          delivered: 0
        });
      }
    } catch (err) {
      console.error('Error fetching status counts:', err);
      // Don't set error for counts, as it's not critical
    }
  };

  // Handle updating order status
  const handleUpdateStatus = async (orderId, status) => {
    try {
      setIsUpdatingOrder(true);
      setError(null);

      const response = await window.ApiClient.updateDeliveryOrder(
        orderId,
        'update_status',
        status
      );

      if (response.success) {
        setSuccess(`Order status updated to ${status}`);

        // After updating status, refresh the data
        // Refresh status counts
        await fetchStatusCounts();

        // Refresh orders for the current tab
        // If we just updated to a new status, we should switch to that tab
        const newTab = status; // Use the new status as the tab
        setSelectedTab(newTab);
        await fetchOrders(newTab);
      } else {
        setError(response.error || 'Failed to update order status');
      }
    } catch (err) {
      console.error('Error updating order status:', err);
      setError('Failed to update order status. Please try again.');
    } finally {
      setIsUpdatingOrder(false);
    }
  };

  // Handle assigning order to self
  const handleAssignToSelf = async (orderId) => {
    try {
      setIsUpdatingOrder(true);
      setError(null);

      const response = await window.ApiClient.updateDeliveryOrder(
        orderId,
        'assign_self'
      );

      if (response.success) {
        setSuccess('Order assigned to you successfully');

        // After assigning, refresh the data
        // Refresh status counts
        await fetchStatusCounts();

        // Refresh orders for the current tab
        await fetchOrders(selectedTab);
      } else {
        setError(response.error || 'Failed to assign order');
      }
    } catch (err) {
      console.error('Error assigning order:', err);
      setError('Failed to assign order. Please try again.');
    } finally {
      setIsUpdatingOrder(false);
    }
  };

  // No need for client-side filtering as we're fetching filtered data from the API

  // If not a delivery boy, show message
  if (!isLoading && !deliveryBoy) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="material-icons-round text-yellow-400">info</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Not a delivery person
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  You are not registered as a delivery person. Please contact the administrator
                  if you believe this is an error.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-6">
      {error && <ErrorAlert message={error} onClose={() => setError(null)} />}
      {success && <SuccessAlert message={success} onClose={() => setSuccess(null)} />}

      {isLoading ? (
        <LoadingSpinner size="lg" />
      ) : (
        <>
          {/* Delivery Boy Info */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">Delivery Person Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Name</p>
                <p className="font-medium">{deliveryBoy.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Phone</p>
                <p className="font-medium">{deliveryBoy.phone_number}</p>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="flex -mb-px">
              <button
                onClick={() => setSelectedTab('processing')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  selectedTab === 'processing'
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Processing
                {statusCounts.processing > 0 && (
                  <span className="ml-2 bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs">
                    {statusCounts.processing}
                  </span>
                )}
              </button>
              <button
                onClick={() => setSelectedTab('out_for_delivery')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  selectedTab === 'out_for_delivery'
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Out for Delivery
                {statusCounts.out_for_delivery > 0 && (
                  <span className="ml-2 bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs">
                    {statusCounts.out_for_delivery}
                  </span>
                )}
              </button>
              <button
                onClick={() => setSelectedTab('delivered')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  selectedTab === 'delivered'
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Delivered
                {statusCounts.delivered > 0 && (
                  <span className="ml-2 bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs">
                    {statusCounts.delivered}
                  </span>
                )}
              </button>
            </nav>
          </div>

          {/* Orders List */}
          {orders.length === 0 ? (
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <p className="text-gray-500">
                No orders with status "{selectedTab.replace('_', ' ')}"
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {orders.map(order => (
                <div key={order.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">Order #{order.order_number}</h3>
                        <p className="text-sm text-gray-600">
                          {new Date(order.created_at).toLocaleString()}
                        </p>
                      </div>
                      <div className="flex items-center">
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            order.order_status === 'processing'
                              ? 'bg-blue-100 text-blue-800'
                              : order.order_status === 'out_for_delivery'
                              ? 'bg-yellow-100 text-yellow-800'
                              : order.order_status === 'delivered'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {order.order_status.replace('_', ' ')}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border-b border-gray-200">
                    <h4 className="font-medium mb-2">Delivery Address</h4>
                    {order.address ? (
                      <div>
                        <p className="text-sm">{order.address.full_name}</p>
                        <p className="text-sm">{order.address.phone}</p>
                        <p className="text-sm">{order.address.address}</p>
                        <p className="text-sm">{order.address.city}, {order.address.zip_code}</p>
                        {order.address.instructions && (
                          <p className="text-sm mt-1 italic">
                            Note: {order.address.instructions}
                          </p>
                        )}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">No address information</p>
                    )}
                  </div>

                  <div className="p-4 border-b border-gray-200">
                    <h4 className="font-medium mb-2">Order Items</h4>
                    <ul className="space-y-2">
                      {order.items && order.items.map(item => (
                        <li key={item.id} className="flex justify-between text-sm">
                          <span>
                            {item.quantity} x {item.product_name}
                          </span>
                          <span className="font-medium">
                            ₹{item.total_price}
                          </span>
                        </li>
                      ))}
                    </ul>
                    <div className="mt-3 pt-3 border-t border-gray-200 flex justify-between font-medium">
                      <span>Total</span>
                      <span>₹{order.total_amount}</span>
                    </div>
                  </div>

                  <div className="p-4 flex justify-end space-x-2">
                    {!order.delivery_boy_id ? (
                      <button
                        onClick={() => handleAssignToSelf(order.id)}
                        disabled={isUpdatingOrder}
                        className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg"
                      >
                        {isUpdatingOrder ? 'Assigning...' : 'Assign to Me'}
                      </button>
                    ) : (
                      <>
                        {selectedTab === 'processing' && (
                          <button
                            onClick={() => handleUpdateStatus(order.id, 'out_for_delivery')}
                            disabled={isUpdatingOrder}
                            className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg"
                          >
                            {isUpdatingOrder ? 'Updating...' : 'Out for Delivery'}
                          </button>
                        )}
                        {selectedTab === 'out_for_delivery' && (
                          <button
                            onClick={() => handleUpdateStatus(order.id, 'delivered')}
                            disabled={isUpdatingOrder}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg"
                          >
                            {isUpdatingOrder ? 'Updating...' : 'Mark as Delivered'}
                          </button>
                        )}
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
}
