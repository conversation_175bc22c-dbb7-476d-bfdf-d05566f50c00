import type { APIRoute } from 'astro';
import { adminAuthMiddleware } from '../../../middleware/auth';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    const url = new URL(request.url);
    const orderId = url.searchParams.get('orderId');

    if (!orderId) {
      return new Response(JSON.stringify({ error: 'Order ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get order details with customer and address information
    const orderQuery = `
      SELECT 
        o.*,
        u.name as user_name,
        u.email as user_email,
        u.phone_number as user_phone,
        a.full_name,
        a.address,
        a.city,
        a.zip_code,
        a.phone as address_phone,
        a.instructions,
        a.district,
        a.nearest_busstand,
        a.school_name,
        a.whatsapp_number
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      LEFT JOIN user_addresses a ON o.address_id = a.id
      WHERE o.id = ?
    `;

    const orderResult = await locals.runtime.env.SNACKSWIFT_DB.prepare(orderQuery)
      .bind(orderId)
      .first();
    const order = orderResult;

    if (!order) {
      return new Response(JSON.stringify({ error: 'Order not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get order items with product details
    const itemsQuery = `
      SELECT 
        oi.*,
        p.name as product_name,
        p.description as product_description,
        p.image as product_image,
        c.name as category_name
      FROM order_items oi
      LEFT JOIN products p ON oi.product_id = p.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE oi.order_id = ?
      ORDER BY oi.id
    `;

    const itemsResult = await locals.runtime.env.SNACKSWIFT_DB.prepare(itemsQuery)
      .bind(orderId)
      .all();
    const itemRows = itemsResult.results || [];

    // Structure the response
    const orderDetails = {
      ...order,
      customer: {
        name: order.user_name,
        email: order.user_email,
        phone: order.user_phone
      },
      address: order.full_name ? {
        full_name: order.full_name,
        address: order.address,
        city: order.city,
        zip_code: order.zip_code,
        phone: order.address_phone,
        instructions: order.instructions,
        district: order.district,
        nearest_busstand: order.nearest_busstand,
        school_name: order.school_name,
        whatsapp_number: order.whatsapp_number
      } : null,
      items: itemRows
    };

    return new Response(JSON.stringify({ 
      success: true, 
      order: orderDetails 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error fetching order details:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to fetch order details',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}; 