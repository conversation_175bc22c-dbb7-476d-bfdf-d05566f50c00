---
import MainLayout from "../layouts/MainLayout.astro";
import {
  getCategories,
  getFilteredProducts,
  getProductBySlug,
  type Product,
} from "../db/database";
import ProductsPage from "../components/homepage/ProductsPage";
import ProductSkeleton from "../components/common/ProductSkeleton";

// For SSR pages we don't use getStaticPaths()
export const prerender = false;

// Parse the slug to extract filter parameters with improved readability
function parseSlug(slug: string) {
  if (!slug) return {};

  // Split the slug and remove empty segments
  const segments = slug.split("/").filter(Boolean);

  const filters: { [key: string]: any } = {};

  // Check if this is a product detail page (last segment doesn't match our filter patterns)
  if (
    segments.length > 0 &&
    ![
      "category",
      "search",
      "filter",
      "price-range",
      "order-by",
      "page",
    ].includes(segments[0])
  ) {
    // This might be a direct product URL like "/chicken-sandwich"
    filters.productSlug = segments[0];
    return filters;
  }

  // Process segments in pairs (key/value) for category browsing pages
  for (let i = 0; i < segments.length; i += 2) {
    const key = segments[i];
    const value = segments[i + 1];

    if (key && value) {
      switch (key) {
        case "category":
          filters.category = value;
          break;
        case "search":
          filters.search = value;
          break;
        case "order-by": // More readable than 'sort'
          filters.sort = value;
          break;
        case "price-range": // More readable than 'price'
          // Handle price range format: min-max, above-X, under-X
          if (value.includes("-")) {
            const [min, max] = value.split("-");
            if (min && min !== "under") filters.minPrice = parseInt(min);
            if (max && max !== "above") filters.maxPrice = parseInt(max);
          } else if (value.startsWith("above-")) {
            filters.minPrice = parseInt(value.replace("above-", ""));
          } else if (value.startsWith("under-")) {
            filters.maxPrice = parseInt(value.replace("under-", ""));
          }
          break;
        case "filter": // More readable than 'tags'
          filters.tags = value.split(",");
          break;
        case "page":
          filters.page = parseInt(value);
          break;
      }
    }
  }

  return filters;
}

// Define allowed paths for our dynamic routing
const validPaths = [
  "category",
  "search",
  "filter",
  "price-range",
  "order-by",
  "page",
];

// Extract filters from both slug and query parameters
const { slug } = Astro.params;
const url = Astro.url;
const pathname = url.pathname;

// Get filters from slug path parameters
const pathFilters = parseSlug(slug as string);

// Check if this is a product detail page
if (pathFilters.productSlug) {
  // Check if product exists before redirecting
  try {
    const productExists = await getProductBySlug(
      Astro.locals.runtime.env,
      pathFilters.productSlug,
    );

    if (productExists) {
      // Product exists, redirect to the proper product page
      return Astro.redirect(`/product/${pathFilters.productSlug}`);
    } else {
      // Product doesn't exist, redirect to 404 page
      console.warn(`No product found with slug: ${pathFilters.productSlug}`);
      return Astro.redirect("/404");
    }
  } catch (error) {
    console.error(`Error checking product existence: ${error}`);
    return Astro.redirect("/404");
  }
}

// Get URL search parameters (for backward compatibility)
const urlCategory =
  url.searchParams.get("category") || pathFilters.category || "all";
const urlSort = url.searchParams.get("sort") || pathFilters.sort || "newest";
const urlSearch = url.searchParams.get("search") || pathFilters.search || "";
const urlMaxPrice = url.searchParams.has("maxPrice")
  ? parseInt(url.searchParams.get("maxPrice") || "0")
  : pathFilters.maxPrice || 10000;
const urlMinPrice = url.searchParams.has("minPrice")
  ? parseInt(url.searchParams.get("minPrice") || "0")
  : pathFilters.minPrice || 0;
const urlTags = url.searchParams.get("tags")
  ? (url.searchParams.get("tags") || "").split(",")
  : pathFilters.tags || [];
const urlPage = url.searchParams.has("page")
  ? parseInt(url.searchParams.get("page") || "1")
  : pathFilters.page || 1;

// Define fallback categories in case database is unavailable
const fallbackCategories = [
  { id: 1, name: "6th Grade", icon: "📚", color: "#3B82F6" },
  { id: 2, name: "7th Grade", icon: "📖", color: "#10B981" },
  { id: 3, name: "8th Grade", icon: "📝", color: "#F59E0B" },
  { id: 4, name: "9th Grade", icon: "📊", color: "#8B5CF6" },
  { id: 5, name: "10th Grade", icon: "🎓", color: "#EF4444" },
];

// Safely fetch categories from database with fallback
let dbCategories = [];
try {
  if (Astro.locals.runtime && Astro.locals.runtime.env) {
    dbCategories = await getCategories(Astro.locals.runtime.env);
  } else {
    console.warn(
      "Runtime environment not available, using fallback categories",
    );
    dbCategories = fallbackCategories;
  }
} catch (error) {
  console.error("Error fetching categories:", error);
  dbCategories = fallbackCategories;
}

// Create a combined list with "All Books" at the beginning
const categories = [
  { id: "all", name: "All Books" },
  ...dbCategories.map((cat) => ({ id: cat.id.toString(), name: cat.name })),
];

// Fetch initial products based on URL parameters
let initialProducts: Product[] = [];
let totalCount = 0;
let hasMore = false;

try {
  if (Astro.locals.runtime && Astro.locals.runtime.env) {
    const page = urlPage;
    const limit = 30;

    // Construct filter object
    const filters = {
      category: urlCategory,
      sort: urlSort,
      search: urlSearch,
      minPrice: urlMinPrice,
      maxPrice: urlMaxPrice,
      tags: urlTags,
    };

    const result = await getFilteredProducts(Astro.locals.runtime.env, {
      page,
      limit,
      ...filters,
    });

    // Sort products to show featured ones first
    initialProducts = (result.products || []) 

    totalCount = result.totalCount || 0;
    hasMore = result.hasMore || false;
  }
} catch (error) {
  console.error("Error fetching initial products:", error);
}

// Generate SEO-friendly canonical paths for each filter type
function generatePathUrl(params = {} as any) {
  const baseUrl = new URL(url.origin);
  const pathSegments = [];

  // Add segments based on provided filters
  if (params.category && params.category !== "all") {
    pathSegments.push("category", params.category);
  }

  if (params.search) {
    pathSegments.push("search", params.search);
  }

  if (params.sort && params.sort !== "newest") {
    pathSegments.push("order-by", params.sort);
  }

  if (params.tags && params.tags.length > 0) {
    pathSegments.push("filter", params.tags.join(","));
  }

  if (
    (params.minPrice && params.minPrice > 0) ||
    (params.maxPrice && params.maxPrice < 10000)
  ) {
    const minVal = params.minPrice || 0;
    const maxVal = params.maxPrice || 10000;

    if (minVal > 0 && maxVal < 10000) {
      pathSegments.push("price-range", `${minVal}-${maxVal}`);
    } else if (minVal > 0) {
      pathSegments.push("price-range", `above-${minVal}`);
    } else if (maxVal < 10000) {
      pathSegments.push("price-range", `under-${maxVal}`);
    }
  }

  if (params.page && params.page > 1) {
    pathSegments.push("page", params.page.toString());
  }

  // Construct the path
  if (pathSegments.length > 0) {
    baseUrl.pathname = `/${pathSegments.join("/")}`;
  } else {
    baseUrl.pathname = "/"; // Default to /menu for the main products page
  }

  return baseUrl.toString();
}

// Generate the canonical URL for current filters
const canonicalUrl = generatePathUrl({
  category: urlCategory,
  sort: urlSort,
  search: urlSearch,
  minPrice: urlMinPrice,
  maxPrice: urlMaxPrice,
  tags: urlTags,
  page: urlPage,
});

// Build breadcrumb structure for SEO
const breadcrumbs = [
  { name: "Home", url: "/" },
  { name: "Books", url: "/" },
];

// Add category breadcrumb if applicable
if (urlCategory && urlCategory !== "all") {
  const categoryName =
    categories.find((c) => c.id === urlCategory)?.name || urlCategory;
  breadcrumbs.push({
    name: categoryName,
    url: generatePathUrl({ category: urlCategory }),
  });
}

// Add search result breadcrumb if applicable
if (urlSearch) {
  breadcrumbs.push({ name: `Search: ${urlSearch}`, url: canonicalUrl });
}

// Prepare initial data to pass to the React component
const initialData = {
  categories,
  products: initialProducts,
  totalCount,
  hasMore,
  currentUrl: url.toString(),
  canonicalUrl,
  baseUrl: `${url.protocol}//${url.host}`,
  pathMode: true, // Use path-based routing instead of query parameters
  activeFilters: {
    category: urlCategory,
    tags: urlTags,
    minPrice: urlMinPrice,
    maxPrice: urlMaxPrice,
  },
  selectedSort: urlSort,
  searchQuery: urlSearch,
  currentPage: urlPage,
  breadcrumbs,
};

// Determine page title based on filters
let pageTitle = " Sreekar Publishers";
let pageDescription =
  "Browse our comprehensive collection of educational materials for students from 6th to 10th grade.";

if (urlSearch) {
  pageTitle = `Search Results: ${urlSearch}`;
  pageDescription = `Find the best educational books and study materials matching "${urlSearch}" in our catalog.`;
} else if (urlCategory && urlCategory !== "all") {
  const categoryName =
    categories.find((c) => c.id === urlCategory)?.name || urlCategory;
  pageTitle = `${categoryName} Books`;
  pageDescription = `Explore our selection of ${categoryName.toLowerCase()} textbooks, reference books, and study materials designed for academic excellence.`;
}

// Add price range to description if applicable
if (urlMinPrice > 0 || urlMaxPrice < 10000) {
  let priceDesc = "";
  if (urlMinPrice > 0 && urlMaxPrice < 10000) {
    priceDesc = ` priced between ₹${urlMinPrice} and ₹${urlMaxPrice}`;
  } else if (urlMinPrice > 0) {
    priceDesc = ` priced above ₹${urlMinPrice}`;
  } else if (urlMaxPrice < 10000) {
    priceDesc = ` priced under ₹${urlMaxPrice}`;
  }
  pageDescription += priceDesc;
}
---

<MainLayout
  title={`${pageTitle} - Sreekar Publishers`}
  description={pageDescription}
  headerTitle={pageTitle}
  showHeader={true}
  showBackButton={false}
  canonicalURL={canonicalUrl}
  schema={{
    title: `${pageTitle} - Sreekar Publishers`,
    description: pageDescription,
    url: canonicalUrl,
    image: `${url.origin}/images/sreekar-publishers-social-image.png`,
    type: "website",
  }}
>
  <!-- Enhanced Professional Breadcrumbs -->
  <div
    class="breadcrumbs-container bg-gradient-to-r from-blue-50 to-indigo-50 py-3 px-4 border-b border-blue-100"
    aria-label="Breadcrumb"
  >
    <div class="max-w-7xl mx-auto">
      <ol class="list-none p-0 inline-flex items-center">
        {
          breadcrumbs.map((crumb, index) => (
            <li class="flex items-center">
              {index > 0 && (
                <span class="mx-2 text-blue-400">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
              )}
              {index === breadcrumbs.length - 1 ? (
                <span
                  aria-current="page"
                  class="text-blue-800 font-semibold bg-white px-3 py-1.5 rounded-lg shadow-sm border border-blue-200"
                >
                  {crumb.name}
                </span>
              ) : (
                <a
                  href={crumb.url}
                  class="text-blue-600 hover:text-blue-800 hover:bg-white px-3 py-1.5 rounded-lg transition-all duration-200 font-medium"
                >
                  {crumb.name}
                </a>
              )}
            </li>
          ))
        }
      </ol>
    </div>
  </div>

  <!-- Main Content with Professional Animation -->
  <div class="page-content">
    <ProductsPage client:load initialData={initialData as any}>
      <ProductSkeleton slot="fallback" count={12} />
    </ProductsPage>
  </div>
</MainLayout>

<style is:inline>
  /* Professional Educational Design System */

  /* Enhanced scrollbar hiding */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Professional range slider styling */
  input[type="range"] {
    -webkit-appearance: none;
    height: 8px;
    background: linear-gradient(to right, #e2e8f0, #cbd5e1);
    border-radius: 6px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 24px;
    width: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow:
      0 4px 8px rgba(59, 130, 246, 0.3),
      0 0 0 3px white,
      0 0 0 4px rgba(59, 130, 246, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow:
      0 6px 12px rgba(59, 130, 246, 0.4),
      0 0 0 3px white,
      0 0 0 5px rgba(59, 130, 246, 0.3);
  }

  input[type="range"]::-moz-range-thumb {
    height: 24px;
    width: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow:
      0 4px 8px rgba(59, 130, 246, 0.3),
      0 0 0 3px white;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
  }

  input[type="range"]::-webkit-slider-runnable-track {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
  }

  /* Enhanced product animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  /* Professional product card styling */
  .product-skeleton,
  .product-card {
    contain: layout;
    animation: fadeInUp 0.6s ease-out;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .product-card:hover {
    transform: translateY(-4px);
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Enhanced breadcrumb styling */
  .breadcrumbs-container {
    background: linear-gradient(135deg, #eff6ff 0%, #e0f2fe 100%);
    border-bottom: 1px solid #bfdbfe;
    backdrop-filter: blur(8px);
  }

  /* Professional loading shimmer effect */
  .shimmer {
    background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }

  /* Educational theme colors */
  :root {
    --educational-primary: #3b82f6;
    --educational-secondary: #1e40af;
    --educational-accent: #60a5fa;
    --educational-success: #10b981;
    --educational-warning: #f59e0b;
    --educational-error: #ef4444;
    --educational-gray-50: #f8fafc;
    --educational-gray-100: #f1f5f9;
    --educational-gray-200: #e2e8f0;
    --educational-gray-300: #cbd5e1;
    --educational-gray-400: #94a3b8;
    --educational-gray-500: #64748b;
    --educational-gray-600: #475569;
    --educational-gray-700: #334155;
    --educational-gray-800: #1e293b;
    --educational-gray-900: #0f172a;
  }

  /* Professional typography enhancements */
  .text-educational-primary {
    color: var(--educational-primary);
  }
  .text-educational-secondary {
    color: var(--educational-secondary);
  }
  .bg-educational-primary {
    background-color: var(--educational-primary);
  }
  .bg-educational-secondary {
    background-color: var(--educational-secondary);
  }

  /* Enhanced focus states for accessibility */
  .focus-ring:focus {
    outline: none;
    box-shadow:
      0 0 0 3px rgba(59, 130, 246, 0.1),
      0 0 0 2px var(--educational-primary);
  }

  /* Professional button hover effects */
  .btn-educational {
    background: linear-gradient(
      135deg,
      var(--educational-primary),
      var(--educational-secondary)
    );
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-educational:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
  }

  /* Smooth page transitions */
  .page-content {
    animation: fadeInUp 0.5s ease-out;
  }
</style>
