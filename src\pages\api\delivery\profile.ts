import type { APIRoute } from 'astro';
import { authMiddleware } from '../../../middleware/auth';
import {
  getDeliveryBoyById
} from '../../../db/database';

export const prerender = false;

/**
 * Get delivery boy profile
 */
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Authenticate user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    const { user } = authResult as { user: { id: number } };

    // Check if the user is a delivery boy
    const deliveryBoy = await getDeliveryBoyById(locals.runtime.env, user.id);

    if (!deliveryBoy) {
      return new Response(JSON.stringify({ error: "Not authorized as delivery boy" }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Return delivery boy profile
    return new Response(JSON.stringify({
      success: true,
      deliveryBoy
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching delivery profile:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch delivery profile' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
