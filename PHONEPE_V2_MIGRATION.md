# PhonePe v2 API Integration Migration Guide

## Overview
This document outlines the migration from PhonePe v1 API to v2 API for payment processing. The v2 API introduces significant changes in authentication, request structure, and endpoints.

## Key Changes in v2 API

### 1. Authentication Method
- **v1**: SHA256 checksum with salt key verification
- **v2**: OAuth 2.0 Bearer token authentication

### 2. API Endpoints
- **v1**: `/pg/v1/pay`
- **v2**: `/checkout/v2/pay`

### 3. Request Structure
- **v1**: Base64 encoded payload with X-VERIFY header
- **v2**: Direct JSON payload with Authorization header

### 4. Response Structure
- **v1**: Nested response with `data.instrumentResponse.redirectInfo.url`
- **v2**: Direct response with `redirectUrl`, `orderId`, `state`, `expireAt`

## Environment Configuration

Update your environment variables:

```bash
# v2 API Configuration
PHONEPE_API_URL=https://api.phonepe.com/apis/pg
PHONEPE_AUTH_URL=https://api.phonepe.com/apis/pg/auth
PHONEPE_MERCHANT_ID=M22TVSLFNFUG7
PHONEPE_CLIENT_ID=M22TVSLFNFUG7
PHONEPE_CLIENT_SECRET=your-client-secret-here

# For UAT/Testing
PHONEPE_API_URL=https://api-preprod.phonepe.com/apis/pg-sandbox
PHONEPE_AUTH_URL=https://api-preprod.phonepe.com/apis/pg-sandbox/auth
```

## Updated Implementation

### 1. Authorization Flow
```javascript
async function getPhonePeAccessToken(config) {
  const authPayload = {
    grant_type: "client_credentials",
    client_id: config.CLIENT_ID,
    client_secret: config.CLIENT_SECRET
  };

  const response = await axios.post(`${config.PHONEPE_AUTH_URL}/token`, authPayload, {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  });

  return response.data.access_token;
}
```

### 2. Payment Initiation
```javascript
const paymentPayload = {
  merchantOrderId: merchantTxnId, // Changed from merchantTransactionId
  amount: amountInPaise,
  expireAfter: 1200, // Optional, defaults to 1200 seconds
  metaInfo: {
    udf1: `order_${order.id}`,
    udf2: user.name || "Customer",
    udf3: phoneNumber || "",
    udf4: `items_${order.items?.length || 0}`,
    udf5: "srikar_publications"
  },
  paymentFlow: {
    type: "PG_CHECKOUT",
    message: `Payment for Order #${order.id}`,
    merchantUrls: {
      redirectUrl: `${baseUrl}/checkout/status?transactionId=${merchantTxnId}`
    }
  }
};

const response = await axios.post(`${config.PHONEPE_API_URL}/checkout/v2/pay`, paymentPayload, {
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `O-Bearer ${accessToken}`
  }
});
```

### 3. Order Status Check
```javascript
const statusResponse = await axios.get(
  `${config.PHONEPE_API_URL}/checkout/v2/order/${merchantOrderId}/status?details=true&errorContext=true`,
  {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `O-Bearer ${accessToken}`
    }
  }
);
```

## Response Handling

### v2 Payment Initiation Response
```json
{
  "orderId": "OMO123456789",
  "state": "PENDING",
  "expireAt": 1703756259307,
  "redirectUrl": "https://mercury.phonepe.com/transact/..."
}
```

### v2 Status Check Response
```json
{
  "orderId": "OMO123456789",
  "state": "COMPLETED", // PENDING, COMPLETED, FAILED
  "amount": 1000,
  "expireAt": 1703756259307,
  "metaInfo": {
    "udf1": "order_123",
    "udf2": "Customer Name",
    "udf3": "9999999999",
    "udf4": "items_2",
    "udf5": "srikar_publications"
  },
  "paymentDetails": [
    {
      "transactionId": "OM123456789",
      "paymentMode": "UPI_INTENT",
      "timestamp": 1703756259307,
      "amount": 1000,
      "state": "COMPLETED"
    }
  ]
}
```

## Testing

1. **Run the test script**:
   ```bash
   node test-phonepe-v2.js
   ```

2. **Update your client secret** in the test script before running

3. **Test flow**:
   - Get access token
   - Initiate payment
   - Check order status

## Migration Checklist

- [x] Update API endpoints to v2
- [x] Implement OAuth 2.0 authentication
- [x] Update request payload structure
- [x] Update response handling
- [x] Update status check endpoint
- [ ] Test with your merchant credentials
- [ ] Update webhook handling (if needed)
- [ ] Deploy to production

## Important Notes

1. **Access Token Expiry**: Access tokens have an expiration time. Implement token refresh logic for production.

2. **Error Handling**: v2 API has different error response structures. Update error handling accordingly.

3. **Webhook Compatibility**: Webhook structure may remain the same, but verify with your implementation.

4. **Rate Limiting**: Be aware of API rate limits and implement appropriate retry logic.

5. **Security**: Store client secrets securely and never expose them in client-side code.

## Support

For issues with the v2 API integration:
1. Check PhonePe developer documentation
2. Verify merchant credentials and permissions
3. Test in UAT environment first
4. Contact PhonePe support if needed

## Next Steps

1. Update your environment variables with correct client credentials
2. Test the integration in UAT environment
3. Deploy to production after successful testing
4. Monitor payment flows for any issues
