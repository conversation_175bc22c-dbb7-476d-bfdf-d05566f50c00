import type { APIRoute } from 'astro';
import { adminAuthMiddleware } from '../../../../middleware/auth';
import {
  getAdminOrderById,
  updateOrderStatus,
  updateOrderPaymentStatus
} from '../../../../db/database';

export const prerender = false;

/**
 * Get a specific order by ID (admin access)
 */
export const GET: APIRoute = async ({ request, params, locals }) => {
  try {
    // Authenticate admin user
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get order ID from URL params
    const orderId = parseInt(params.id || '');

    if (isNaN(orderId)) {
      return new Response(JSON.stringify({ error: "Invalid order ID" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get the order without user ID restriction (admin can see all orders)
    const order = await getAdminOrderById(locals.runtime.env, orderId);

    if (!order) {
      return new Response(JSON.stringify({ error: "Order not found" }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get customer information
    const { results: customerInfo } = await locals.runtime.env.SNACKSWIFT_DB.prepare(
      "SELECT id, name, email, phone_number phone FROM users WHERE id = ?"
    ).bind(order.user_id).all();

    // Add customer info to the order
    const orderWithCustomer = {
      ...order,
      customer: customerInfo?.[0] || null
    };

    return new Response(JSON.stringify({
      success: true,
      order: orderWithCustomer
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=10' // Short cache for better performance
      }
    });

  } catch (error) {
    console.error('Error retrieving order:', error);
    return new Response(JSON.stringify({ error: "Failed to retrieve order" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/**
 * Update an order (admin access - change status or payment status)
 */
export const PATCH: APIRoute = async ({ request, params, locals }) => {
  try {
    // Authenticate admin user
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }    // Get order ID from URL params
    const orderId = parseInt(params.id || '');

    if (isNaN(orderId)) {
      return new Response(JSON.stringify({ error: "Invalid order ID" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get request data
    const requestData = await request.json() as { action: string; status?: string; reason?: string };
    const { action, status, reason } = requestData;

    // Handle based on action
    if (action === 'update_status') {
      if (!status || typeof status !== 'string') {
        return new Response(JSON.stringify({ error: "Valid status is required" }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const success = await updateOrderStatus(locals.runtime.env, orderId, status);

      if (!success) {
        return new Response(JSON.stringify({ error: "Failed to update order status" }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      return new Response(JSON.stringify({
        success: true,
        message: `Order status updated to '${status}'`
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    else if (action === 'update_payment') {
      if (!status || typeof status !== 'string') {
        return new Response(JSON.stringify({ error: "Valid payment status is required" }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const success = await updateOrderPaymentStatus(locals.runtime.env, orderId, status);

      if (!success) {
        return new Response(JSON.stringify({ error: "Failed to update payment status" }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      return new Response(JSON.stringify({
        success: true,
        message: `Payment status updated to '${status}'`
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    else if (action === 'cancel') {
      if (!reason || typeof reason !== 'string' || reason.trim() === '') {
        return new Response(JSON.stringify({ error: "Cancellation reason is required" }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // For admin cancellation, we'll update both the order status and set the cancellation reason
      const result = await locals.runtime.env.SNACKSWIFT_DB.prepare(`
        UPDATE orders
        SET order_status = 'cancelled',
            cancel_reason = ?,
            updated_at = datetime('now')
        WHERE id = ?
      `).bind(reason, orderId).run();

      if (!result || !result.success) {
        return new Response(JSON.stringify({ error: "Failed to cancel order" }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Also update payment status if it was pending
      await locals.runtime.env.SNACKSWIFT_DB.prepare(`
        UPDATE orders
        SET payment_status = 'cancelled'
        WHERE id = ? AND payment_status = 'pending'
      `).bind(orderId).run();

      return new Response(JSON.stringify({
        success: true,
        message: "Order cancelled successfully"
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    else {
      return new Response(JSON.stringify({ error: "Invalid action" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Error updating order:', error);
    return new Response(JSON.stringify({ error: "Failed to update order" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/**
 * Delete an order (admin access)
 */
export const DELETE: APIRoute = async ({ request, params, locals }) => {
  try {
    // Authenticate admin user
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get order ID from URL params
    const orderId = parseInt(params.id || '');

    if (isNaN(orderId)) {
      return new Response(JSON.stringify({ error: "Invalid order ID" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if order exists
    const order = await getAdminOrderById(locals.runtime.env, orderId);

    if (!order) {
      return new Response(JSON.stringify({ error: "Order not found" }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // First delete order items
    const deleteItemsResult = await locals.runtime.env.SNACKSWIFT_DB.prepare(`
      DELETE FROM order_items WHERE order_id = ?
    `).bind(orderId).run();

    if (!deleteItemsResult.success) {
      return new Response(JSON.stringify({ error: "Failed to delete order items" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Then delete the order
    const deleteOrderResult = await locals.runtime.env.SNACKSWIFT_DB.prepare(`
      DELETE FROM orders WHERE id = ?
    `).bind(orderId).run();

    if (!deleteOrderResult.success) {
      return new Response(JSON.stringify({ error: "Failed to delete order" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: "Order deleted successfully"
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error deleting order:', error);
    return new Response(JSON.stringify({ error: "Failed to delete order" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
