import type { APIRoute } from 'astro';
import { adminAuthMiddleware } from '../../../middleware/auth';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get request data
    const { orderIds } = await request.json() as { orderIds: number[] };
    
    if (!Array.isArray(orderIds) || orderIds.length === 0) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: 'Order IDs are required' 
        }),
        { status: 400 }
      );
    }

    // Split order IDs into chunks to prevent database parameter limits
    const BATCH_SIZE = 50; // Process 50 order IDs at a time
    const chunks = [];
    for (let i = 0; i < orderIds.length; i += BATCH_SIZE) {
      chunks.push(orderIds.slice(i, i + BATCH_SIZE));
    }

    console.log(`Processing ${orderIds.length} order IDs in ${chunks.length} batches of ${BATCH_SIZE}`);

    // Process each chunk and collect results
    const allOrderItems = [];
    
    for (const chunk of chunks) {
      try {
        // Create placeholders for the IN clause
        const placeholders = chunk.map(() => '?').join(',');
        
        // Build query to get order items for this chunk
        const query = `
          SELECT oi.*, 
                 p.name as product_name, 
                 p.image as product_image,
                 p.category_id,
                 c.name as category_name
          FROM order_items oi
          LEFT JOIN products p ON oi.product_id = p.id
          LEFT JOIN categories c ON p.category_id = c.id
          WHERE oi.order_id IN (${placeholders})
          ORDER BY oi.order_id, oi.id
        `;
        
        // Execute query for this chunk
        const result = await locals.runtime.env.SNACKSWIFT_DB.prepare(query)
          .bind(...chunk)
          .all();
        
        // Add results to our collection
        if (result.results) {
          allOrderItems.push(...result.results);
        }
        
      } catch (chunkError) {
        console.error(`Error processing chunk ${chunk}:`, chunkError);
        // Continue with other chunks, but log the error
      }
    }

    console.log(`Successfully fetched ${allOrderItems.length} order items`);
    
    return new Response(
      JSON.stringify({
        success: true,
        orderItems: allOrderItems
      }),
      { 
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
  } catch (error) {
    console.error('Error fetching order items:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: 'Failed to fetch order items' 
      }),
      { status: 500 }
    );
  }
}; 