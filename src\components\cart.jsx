import React, { useState, useEffect } from "react";

export default function Cart() {
  const [cartItems, setCartItems] = useState([]);
  console.log("🚀 ~ Cart ~ cartItems:", cartItems)
  const [isLoading, setIsLoading] = useState(true);
  const [pricing, setPricing] = useState({
    subtotal: 0,
    deliveryFee: 0,
    discount: 0,
    couponCode: "",
    total: 0,
  });
  const [promoCode, setPromoCode] = useState("");
  const [promoError, setPromoError] = useState("");
  const [promoSuccess, setPromoSuccess] = useState("");

  // No longer managing delivery locations in cart page

  // Load cart items and pricing on component mount
  useEffect(() => {
    const timer = setTimeout(async () => {
      if (typeof window !== "undefined" && window.CartUtils) {
        const items = window.CartUtils.getCartItems();
        const currentPricing = window.CartUtils.getPricing();
        const coupon = await window.CartUtils.getAppliedCoupon();

        setCartItems(items);
        setPricing(currentPricing);

        // Set promo code input if there's an active coupon
        if (coupon) {
          setPromoCode(coupon.code);
          setPromoSuccess(coupon.description);
        }
      }
      setIsLoading(false);
    }, 500); // Increased timeout to ensure CartUtils is fully loaded

    // Add event listener for pricing updates
    const handlePricingUpdate = (event) => {
      console.log("Pricing updated event received:", event.detail);
      setPricing(event.detail);
    };

    if (typeof window !== "undefined") {
      window.addEventListener("pricing-updated", handlePricingUpdate);
    }

    return () => {
      clearTimeout(timer);
      if (typeof window !== "undefined") {
        window.removeEventListener("pricing-updated", handlePricingUpdate);
      }
    };
  }, []);

  // Delivery locations are now managed in the checkout page

  // Update item quantity
  const updateQuantity = (productId, newQuantity) => {
    if (typeof window !== "undefined" && window.CartUtils) {
      // Add haptic feedback if available
      if ("vibrate" in navigator) {
        navigator.vibrate(40);
      }

      const updatedItems = window.CartUtils.updateQuantity(
        productId,
        newQuantity
      );
      const updatedPricing = window.CartUtils.getPricing();

      setCartItems(updatedItems);
      setPricing(updatedPricing);

      // Show notification
      showNotification(
        newQuantity === 0 ? "Item removed from cart" : "Quantity updated"
      );
    }
  };

  // Remove item from cart
  const removeItem = (productId) => {
    if (typeof window !== "undefined" && window.CartUtils) {
      // Add haptic feedback if available
      if ("vibrate" in navigator) {
        navigator.vibrate([40, 30, 40]);
      }

      const updatedItems = window.CartUtils.removeFromCart(productId);
      const updatedPricing = window.CartUtils.getPricing();

      setCartItems(updatedItems);
      setPricing(updatedPricing);

      // Show notification
      showNotification("Item removed from cart");
    }
  };

  // Clear entire cart
  const clearCart = () => {
    if (typeof window !== "undefined" && window.CartUtils) {
      window.CartUtils.clearCart();
      setCartItems([]);
      setPricing({
        subtotal: 0,
        deliveryFee: 0,
        discount: 0,
        couponCode: "",
        total: 0,
      });
      setPromoCode("");
      setPromoSuccess("");
      setPromoError("");

      // Show notification
      showNotification("Cart cleared");
    }
  };

  // Location selection is now handled in the checkout page

  // Apply promo code
  const applyPromoCode = async () => {
    if (!promoCode.trim()) {
      setPromoError("Please enter a promo code");
      setPromoSuccess("");
      return;
    }

    if (typeof window !== "undefined" && window.CartUtils) {
      try {
        const result = await window.CartUtils.applyCoupon(promoCode);

        if (result.success) {
          setPromoError("");
          setPromoSuccess(result.coupon.description);
          setPricing(window.CartUtils.getPricing());
          showNotification("Promo code applied successfully!");

          // Add haptic feedback if available
          if ("vibrate" in navigator) {
            navigator.vibrate(100);
          }
        } else {
          setPromoError(result.message);
          setPromoSuccess("");
        }
      } catch (error) {
        console.error("Error applying coupon:", error);
        setPromoError("Failed to apply coupon. Please try again.");
      }
    }
  };

  // Remove promo code
  const removePromoCode = () => {
    if (typeof window !== "undefined" && window.CartUtils) {
      window.CartUtils.removeCoupon();
      setPricing(window.CartUtils.getPricing());
      setPromoCode("");
      setPromoSuccess("");
      setPromoError("");
      showNotification("Promo code removed");
    }
  };

  // Show notification
  const showNotification = (message) => {
    if (typeof window !== "undefined") {
      let toastContainer = document.querySelector(".toast-container");

      if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.className =
          "toast-container fixed bottom-24 sm:bottom-20 left-0 right-0 flex flex-col items-center z-50 pointer-events-none px-4";
        document.body.appendChild(toastContainer);
      }

      const toast = document.createElement("div");
      toast.className =
        "bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-lg opacity-0 transition-all duration-300 transform translate-y-4 mb-2 flex items-center max-w-md w-full justify-center";

      const icon = document.createElement("span");
      icon.className = "material-icons-round mr-2 text-[16px] md:text-[18px]";

      if (message.includes("success") || message.includes("applied")) {
        icon.textContent = "check_circle";
      } else if (message.includes("removed")) {
        icon.textContent = "remove_circle";
      } else {
        icon.textContent = "info";
      }

      toast.appendChild(icon);

      const textSpan = document.createElement("span");
      textSpan.textContent = message;
      toast.appendChild(textSpan);

      toastContainer.appendChild(toast);

      setTimeout(() => {
        toast.classList.remove("opacity-0", "translate-y-4");
        toast.classList.add("opacity-95");
      }, 10);

      setTimeout(() => {
        toast.classList.add("opacity-0", "translate-y-4");
        setTimeout(() => {
          toast.remove();
        }, 300);
      }, 2500);
    }
  };

  return (
    <div className="pb-24 sm:pb-20 max-w-md mx-auto">
      {/* Cart Header with modern design */}
      {cartItems.length > 0 && (
        <div className="flex items-center justify-between pt-2 pb-4 px-4">
          <div className="flex items-center">
            <span className="text-[#5466F7] font-medium">
              {cartItems.length} {cartItems.length === 1 ? "item" : "items"}
            </span>
          </div>
          <button
            onClick={clearCart}
            className="flex items-center text-sm text-gray-500 hover:text-red-500 transition-colors py-1.5 px-2.5 rounded-full hover:bg-red-50"
          >
            <span className="material-icons-round text-sm mr-1">delete</span>
            Clear All
          </button>
        </div>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-20">
          <div className="w-14 h-14 border-4 border-gray-100 border-t-[#5466F7] border-b-[#5466F7] rounded-full animate-spin mb-4"></div>
          <p className="text-gray-600 font-medium">Loading your cart...</p>
        </div>
      ) : (
        <>
          {/* Empty Cart State */}
          {cartItems.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
              <div className="w-24 h-24 bg-gray-50 rounded-full flex items-center justify-center mb-6 border border-gray-100 shadow-inner">
                <span className="material-icons-round text-gray-400 text-[40px]">
                  shopping_bag
                </span>
              </div>
              <h2 className="text-xl font-semibold text-gray-800 mb-3">
                Your cart is empty
              </h2>
              <p className="text-gray-500 mb-8 max-w-xs">
                Looks like you haven't added any items to your cart yet. Start
                browsing our educational materials!
              </p>
              <a
                href="/"
                className="bg-[#5466F7] text-white px-8 py-3.5 rounded-xl font-medium hover:bg-[#4555e2] transition-all flex items-center justify-center shadow-sm hover:shadow"
              >
                <span className="material-icons-round mr-2">
                  school
                </span>
                Browse Materials
              </a>
            </div>
          ) : (
            <div className="px-4">
              {/* Cart Items with improved card design */}
              <div className="space-y-3 mb-8">
                {cartItems.map((item) => (
                  <div
                    key={item.id}
                    className="bg-white rounded-xl border border-gray-100 shadow-sm overflow-hidden transition-all hover:border-gray-200"
                  >
                    <div className="flex p-3">
                     
                      <div className="ml-3 flex-1 flex flex-col justify-between py-0.5">
                        <div className="flex flex-col">
                          <p className="font-medium text-gray-800 line-clamp-2">
                            {item.name}
                          </p>
                          {item.unit_type && item.unit_value && (
                            <div className="text-xs text-blue-600 mt-1">
                              {item.unit_type === "quantity"
                                ? `${item.unit_value} ${
                                    item.unit_value > 1 ? "items" : "item"
                                  }`
                                : `${item.unit_value}${item.unit_type}`}
                            </div>
                          )}
                          <p className="text-sm text-gray-500 mt-1">
                            ₹ {item.price.toFixed(2)}
                          </p>
                        </div>

                        <div className="flex justify-between items-end w-full">
                          <button
                            onClick={() => removeItem(item.id)}
                            className="flex items-center text-xs text-gray-500 hover:text-red-500 transition-colors py-1 pr-2"
                          >
                            <span className="material-icons-round text-sm mr-0.5">
                              delete_outline
                            </span>
                            Remove
                          </button>

                          <div className="flex items-center gap-2">
                            <label htmlFor={`qty-${item.id}`} className="text-xs text-gray-600 font-medium">
                              Qty:
                            </label>
                            <input
                              id={`qty-${item.id}`}
                              type="number"
                              min="1"
                              max="999"
                              value={item.quantity}
                              onChange={(e) => {
                                const value = e.target.value;
                                // Allow empty input while typing
                                // if (value === '') {
                                //   return;
                                // }
                                let newQuantity = parseInt(value);
                                // if (newQuantity > 999) newQuantity = 999;
                                updateQuantity(item.id, newQuantity);
                              }}
                              onBlur={(e) => {
                                const value = e.target.value;
                                // If empty or invalid, set to 1
                                // if (!value || isNaN(parseInt(value)) || parseInt(value) < 1) {
                                //   updateQuantity(item.id, 1);
                                // }
                              }}
                              onKeyDown={(e) => {
                                // Only allow numbers, backspace, delete, arrows and tab
                                if (!/[0-9]/.test(e.key) && 
                                    e.key !== 'Backspace' && 
                                    e.key !== 'Delete' && 
                                    e.key !== 'ArrowUp' && 
                                    e.key !== 'ArrowDown' && 
                                    e.key !== 'Tab') {
                                  e.preventDefault();
                                }
                              }}
                              className="w-14 px-2 py-1 text-center border border-gray-300 rounded-md text-sm font-medium focus:ring-2 focus:ring-[#5466F7] focus:border-transparent outline-none bg-white"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="px-3 py-2.5 flex justify-end items-center bg-gray-50 border-t border-gray-100">
                      <p className="text-gray-700 font-semibold">
                        ₹ {item.quantity > 0 ? (item.price * item.quantity).toFixed(2) : 0.00}
                      </p>
                    </div>
                  </div>
                ))}
              </div> 

              <div className="bg-white rounded-xl border border-gray-100 shadow-sm p-4 mb-4">
                <h3 className="text-gray-800 font-medium mb-3 flex items-center">
                  <span className="material-icons-round text-[#5466F7] mr-2">
                    redeem
                  </span>
                  Apply Coupon
                </h3>
                {promoSuccess ? (
                  <div className="flex items-center justify-between bg-green-50 p-3 rounded-xl border border-green-100">
                    <div className="flex items-center">
                      <span className="material-icons-round text-green-500 mr-2">
                        check_circle
                      </span>
                      <div>
                        <p className="text-sm font-medium text-gray-700">
                          {promoCode}
                        </p>
                        <p className="text-xs text-green-600">{promoSuccess}</p>
                      </div>
                    </div>
                    <button
                      onClick={removePromoCode}
                      className="ml-2 flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                    >
                      <span className="material-icons-round text-gray-500">
                        close
                      </span>
                    </button>
                  </div>
                ) : (
                  <div className="flex w-full sm:w-[60%]">
                    <input
                      type="text"
                      value={promoCode}
                      onChange={(e) => {
                        setPromoCode(e.target.value);
                        setPromoError(""); // Clear previous errors when typing
                      }}
                      placeholder="Enter promo code"
                      className="flex-1 border border-gray-200 rounded-l-xl px-3 sm:px-4 py-3 focus:ring-2 focus:ring-[#5466F7] focus:border-transparent outline-none bg-gray-50 font-medium text-sm sm:text-base"
                    />
                    <button
                      onClick={applyPromoCode}
                      className="bg-[#5466F7] text-white px-3 sm:px-5 py-3 rounded-r-xl font-medium hover:bg-[#4555e2] transition-colors whitespace-nowrap text-sm sm:text-base"
                    >
                      Apply
                    </button>
                  </div>
                )}
                {promoError && (
                  <p className="text-red-500 text-sm mt-2 flex items-center">
                    <span className="material-icons-round text-sm mr-1">
                      error
                    </span>
                    {promoError}
                  </p>
                )}
              </div>

              {/* Delivery location selection moved to checkout page */}
              <div className="bg-white rounded-xl border border-gray-100 shadow-sm p-4 mb-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-gray-800 flex items-center">
                    <span className="material-icons-round text-[#5466F7] mr-2">
                      local_shipping
                    </span>
                    Delivery Information
                  </h3>
                  <span className="text-xs bg-blue-100 text-[#5466F7] px-2 py-1 rounded-full">
                    Set at checkout
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  Delivery location and fees will be confirmed during checkout.
                </p>
              </div>

              {/* Order Summary with improved styling */}
              <div className="bg-white rounded-xl border border-gray-100 shadow-sm p-5 mb-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <span className="material-icons-round mr-2 text-[#5466F7]">
                    receipt
                  </span>
                  Order Summary
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between py-1">
                    <span className="text-gray-500">Subtotal</span>
                    <span className="text-gray-800 font-medium">
                      ₹ {pricing.subtotal.toFixed(2)}
                    </span>
                  </div>
                  {/* <div className="flex justify-between py-1">
                    <span className="text-gray-500">Delivery Fee</span>
                    <span>
                      {pricing.deliveryFee > 0 ? (
                        <span className="text-gray-800 font-medium">
                          ₹ {pricing.deliveryFee.toFixed(2)}
                        </span>
                      ) : (
                        <span className="text-green-600 font-medium">Free</span>
                      )}
                    </span>
                  </div> */}
                  {pricing.discount > 0 && (
                    <div className="flex justify-between text-green-600 py-1">
                      <span className="flex items-center">
                        <span className="material-icons-round text-[18px] mr-1">
                          discount
                        </span>
                        Discount
                      </span>
                      <span className="font-medium">
                        -₹ {pricing.discount.toFixed(2)}
                      </span>
                    </div>
                  )}
                  <div className="border-t border-gray-100 pt-4 mt-2">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-800 font-semibold">Total</span>
                      <span className="text-xl text-[#5466F7] font-bold">
                        ₹{" "}
                        {pricing.total.toFixed(2) -
                          pricing.deliveryFee.toFixed(2)}
                      </span>
                    </div>
                  </div>

                  {/* Free Delivery Notification */}
                  {pricing.isFreeDelivery && (
                    <div className="mt-4 p-2.5 bg-green-50 border border-green-100 rounded-lg text-green-700 text-sm flex items-center">
                      <span className="material-icons-round text-sm mr-1.5">
                        local_shipping
                      </span>
                      <span>Free delivery applied!</span>
                    </div>
                  )}

                  {/* Free Delivery Threshold Notification */}
                  {!pricing.isFreeDelivery &&
                    pricing.freeDeliveryThreshold > 0 && (
                      <div className="mt-4 p-2.5 bg-blue-50 border border-blue-100 rounded-lg text-blue-700 text-sm flex items-center">
                        <span className="material-icons-round text-sm mr-1.5">
                          info
                        </span>
                        <span>
                          Add ₹
                          {(
                            pricing.freeDeliveryThreshold - pricing.subtotal
                          ).toFixed(2)}{" "}
                          more to get free delivery!
                        </span>
                      </div>
                    )}
                </div>
              </div>

              {/* Checkout Button with improved styling */}
              <a
                href={cartItems.some(item => !item.quantity || item.quantity < 1) ? "#" : "/checkout"}
                className={`w-full ${
                  cartItems.some(item => !item.quantity || item.quantity < 1)
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-[#5466F7] hover:bg-[#4555e2]'
                } text-white py-4 rounded-xl font-medium transition-all flex items-center justify-center shadow-sm hover:shadow mb-4`}
                onClick={(e) => {
                  if (cartItems.some(item => !item.quantity || item.quantity < 1)) {
                    e.preventDefault();
                    showNotification("Please ensure all items have valid quantities");
                  }
                }}
              >
                <span className="material-icons-round mr-2">
                  shopping_cart_checkout
                </span>
                Proceed to Checkout (₹{" "}
                {pricing.total.toFixed(2) - pricing.deliveryFee.toFixed(2)})
              </a>
            </div>
          )}
        </>
      )}
    </div>
  );
}
