import type { APIRoute } from 'astro';
import { 
  getPaymentMethodSettings, 
  updatePaymentMethodSettings,
  getPaymentMethodSettingsWithCategories,
  updateCategoryPaymentMethodSettings,
  deleteCategoryPaymentMethodSettings
} from '../../../db/database';
import { adminAuthMiddleware } from '../../../middleware/auth';

export const prerender = false;

/**
 * Get payment method settings (admin only)
 * Query parameters:
 * - withCategories: boolean - if true, returns settings with category-specific overrides
 */
export const GET: APIRoute = async ({ request, locals, url }) => {
  try {
    // Authenticate the user
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return authentication error
    }
    
    const withCategories = url.searchParams.get('withCategories') === 'true';
    
    let settings;
    if (withCategories) {
      settings = await getPaymentMethodSettingsWithCategories(locals.runtime.env);
    } else {
      settings = await getPaymentMethodSettings(locals.runtime.env);
    }
    
    return new Response(JSON.stringify({
      success: true,
      settings
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error getting payment method settings:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to get payment method settings'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/**
 * Update payment method settings (admin only)
 * Body can contain:
 * - For global settings: { online_payment_enabled: boolean, cash_on_delivery_enabled: boolean }
 * - For category settings: { category_id: number, online_payment_enabled: boolean, cash_on_delivery_enabled: boolean }
 * - For deleting category settings: { category_id: number, delete: true }
 */
export const PUT: APIRoute = async ({ request, locals }) => {
  try {
    // Authenticate the user
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return authentication error
    }
    
    // Parse request body
    const requestData = await request.json() as any;
    
    // Check if this is a category-specific operation
    if (requestData.category_id !== undefined) {
      // Validate and convert category ID to number
      const categoryId = typeof requestData.category_id === 'string' 
        ? parseInt(requestData.category_id, 10) 
        : requestData.category_id;
        
      if (typeof categoryId !== 'number' || isNaN(categoryId) || categoryId <= 0) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Invalid category ID'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // Update the requestData with the parsed category ID
      requestData.category_id = categoryId;
      
      // Check if this is a delete operation
      if (requestData.delete === true) {
        const result = await deleteCategoryPaymentMethodSettings(locals.runtime.env, requestData.category_id);
        
        if (!result) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to delete category payment method settings'
          }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          });
        }
        
        return new Response(JSON.stringify({
          success: true,
          message: 'Category payment method settings deleted successfully'
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // Validate payment method settings for category
      if (typeof requestData.online_payment_enabled !== 'boolean' || 
          typeof requestData.cash_on_delivery_enabled !== 'boolean') {
        return new Response(JSON.stringify({
          success: false,
          message: 'Invalid payment method settings'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // Update category-specific settings
      const settings = await updateCategoryPaymentMethodSettings(
        locals.runtime.env, 
        requestData.category_id,
        {
          online_payment_enabled: requestData.online_payment_enabled,
          cash_on_delivery_enabled: requestData.cash_on_delivery_enabled
        }
      );
      
      if (!settings) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Failed to update category payment method settings'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      return new Response(JSON.stringify({
        success: true,
        settings,
        message: 'Category payment method settings updated successfully'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } else {
      // Global settings update
      // Validate request data
      if (typeof requestData.online_payment_enabled !== 'boolean' || 
          typeof requestData.cash_on_delivery_enabled !== 'boolean') {
        return new Response(JSON.stringify({
          success: false,
          message: 'Invalid payment method settings'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // Update global settings
      const settings = await updatePaymentMethodSettings(locals.runtime.env, {
        online_payment_enabled: requestData.online_payment_enabled,
        cash_on_delivery_enabled: requestData.cash_on_delivery_enabled
      });
      
      if (!settings) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Failed to update payment method settings'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      return new Response(JSON.stringify({
        success: true,
        settings,
        message: 'Payment method settings updated successfully'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  } catch (error) {
    console.error('Error updating payment method settings:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to update payment method settings'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
