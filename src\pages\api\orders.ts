import { createOrder, getUserOrders } from '../../db/database';
import { authMiddleware } from '../../middleware/auth';

export const prerender = false;

/**
 * Create a new order
 */
export async function POST({ request, locals }) {
  try {
    // Authenticate user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }
    
    const { user } = authResult;
    
    // Parse request body
    const requestData = await request.json();
    const {
      items,
      total_amount,
      delivery_fee,
      discount_amount,
      coupon_code,
      payment_method,
      address_id
    } = requestData;
    
    // Validate required fields
    if (!items || !Array.isArray(items) || items.length === 0) {
      return new Response(JSON.stringify({ error: "Order items are required" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    if (!payment_method || !['cash', 'online'].includes(payment_method)) {
      return new Response(JSON.stringify({ error: "Valid payment method is required" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    if (!address_id) {
      return new Response(JSON.stringify({ error: "Delivery address is required" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify that the address belongs to the user before creating the order
    try {
      const { results } = await locals.runtime.env.SNACKSWIFT_DB.prepare(
        "SELECT id FROM user_addresses WHERE id = ? AND user_id = ?"
      ).bind(address_id, user.id).all();
      console.log("🚀 ~ POST ~ results:", results)
      
      if (!results || results.length === 0) {
        return new Response(JSON.stringify({ error: "Invalid address. Please select an address that belongs to your account." }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    } catch (error) {
      console.error('Error verifying address:', error);
      return new Response(JSON.stringify({ error: "Failed to verify address ownership" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Create order
    const result = await createOrder(locals.runtime.env, {
      user_id: user.id,
      total_amount,
      delivery_fee: delivery_fee || 0,
      discount_amount: discount_amount || 0,
      coupon_code,
      payment_method,
      address_id,
      items,
      location_id: requestData.location_id || null
    });
    
    if (result.error) {
      return new Response(JSON.stringify({ error: result.error }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return new Response(JSON.stringify({ 
      success: true,
      order: result.order 
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error creating order:', error);
    return new Response(JSON.stringify({ 
      error: "Failed to create order", 
      message: error.message || "Unknown error"
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Get orders for the authenticated user
 */
export async function GET({ request, locals }) {
  try {
    // Authenticate user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }
    
    const { user } = authResult;
    
    // Parse query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get('status') || 'all';
    
    // Get orders
    const orders = await getUserOrders(locals.runtime.env, user.id, status === 'all' ? undefined : status);
    
    return new Response(JSON.stringify({ orders }), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=10' // Short cache for better performance
      }
    });
    
  } catch (error) {
    console.error('Error retrieving orders:', error);
    return new Response(JSON.stringify({ error: "Failed to retrieve orders" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}