import { generateOTP } from '../../../db/database';
import { sendWhatsAppOTP, formatPhoneNumberForWhatsApp, validateWhatsAppPhoneNumber } from '../../../utils/whatsapp';

export const prerender = false;

export async function POST({ request, locals }: { request: Request; locals: any }) {
  try {
    if (!locals.runtime || !locals.runtime.env) {
      return new Response(JSON.stringify({ error: "Runtime environment not available" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    const body = await request.json() as { phoneNumber: string };
    const { phoneNumber } = body;
    
    if (!phoneNumber || phoneNumber.length < 8) {
      return new Response(JSON.stringify({ error: "Valid phone number is required" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate phone number format for WhatsApp
    if (!validateWhatsAppPhoneNumber(phoneNumber)) {
      return new Response(JSON.stringify({ error: "Invalid phone number format" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Generate OTP
    const otp = await generateOTP(locals.runtime.env, phoneNumber);
    
    if (!otp) {
      return new Response(JSON.stringify({ error: "Failed to generate OTP" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    console.log(locals.runtime.env,'locals.runtime.env')
    // Send OTP via WhatsApp
    const whatsappAccessToken = locals.runtime.env.WHATSAPP_ACCESS_TOKEN;
    const whatsappPhoneNumberId = locals.runtime.env.WHATSAPP_PHONE_NUMBER_ID  
    
    if (whatsappAccessToken) {
      try {
        const formattedPhone = formatPhoneNumberForWhatsApp(phoneNumber);
        const whatsappResult = await sendWhatsAppOTP(
          formattedPhone,
          otp,
          whatsappAccessToken,
          whatsappPhoneNumberId
        );
        
        if (whatsappResult.success) {
          console.log(`WhatsApp OTP sent successfully to ${phoneNumber}, Message ID: ${whatsappResult.messageId}`);
          
          return new Response(JSON.stringify({ 
            success: true, 
            message: "OTP sent via WhatsApp successfully",
            messageId: whatsappResult.messageId
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        } else {
          console.error(`Failed to send WhatsApp OTP: ${whatsappResult.error}`);
          // Fall back to console logging for development
          console.log(`OTP for ${phoneNumber}: ${otp}`);
          
          return new Response(JSON.stringify({ 
            success: true, 
            message: "OTP generated (WhatsApp delivery failed)",
            otp, // Include OTP in response when WhatsApp fails
            whatsappError: whatsappResult.error
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        }
      } catch (error) {
        console.error('Error sending WhatsApp OTP:', error);
        // Fall back to console logging
        console.log(`OTP for ${phoneNumber}: ${otp}`);
        
        return new Response(JSON.stringify({ 
          success: true, 
          message: "OTP generated (WhatsApp service unavailable)",
          otp // Include OTP in response when WhatsApp service fails
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    } else {
      // No WhatsApp access token configured, fall back to console logging
      console.log(`OTP for ${phoneNumber}: ${otp}`);
      console.warn('WhatsApp access token not configured. Add WHATSAPP_ACCESS_TOKEN to environment variables.');
      
      return new Response(JSON.stringify({ 
        success: true, 
        message: "OTP sent successfully (development mode)",
        otp // Include OTP in response for development
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
  } catch (error) {
    console.error('Error in send-otp API:', error);
    return new Response(JSON.stringify({ error: "Failed to process request" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
