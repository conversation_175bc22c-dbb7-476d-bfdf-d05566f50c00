import type { APIRoute } from 'astro';
import { getProductById } from '../../../../db/database';
export const prerender = false;
// Get a single product by ID
export const GET: APIRoute = async ({ params, locals }) => {
  // Check admin authentication
  // In a production app, uncomment this code
  /*
  if (!locals.user || !locals.user.isAdmin) {
    return new Response(JSON.stringify({
      error: 'Unauthorized'
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  */

  try {
    const productId = params.id;

    if (!productId) {
      return new Response(JSON.stringify({
        error: 'Product ID is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    const product = await getProductById(locals.runtime.env, productId);

    if (!product) {
      return new Response(JSON.stringify({
        error: 'Product not found'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    return new Response(JSON.stringify({
      product
    }), {
      headers: {
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Error fetching product:', error);
    return new Response(JSON.stringify({
      error: 'Failed to fetch product',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Update a product
export const PUT: APIRoute = async ({ request, params, locals }) => {
  // Check admin authentication
  // In a production app, uncomment this code
  /*
  if (!locals.user || !locals.user.isAdmin) {
    return new Response(JSON.stringify({
      error: 'Unauthorized'
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  */

  try {
    const productId = params.id;

    if (!productId) {
      return new Response(JSON.stringify({
        error: 'Product ID is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Define a type with index signature for product
    interface ProductData {
      name: string;
      url_slug: string;
      description: string;
      price: number;
      old_price?: number;
      image?: string;
      category_id: number;
      is_featured?: boolean;
      is_new?: boolean;
      is_on_sale?: boolean;
      is_available?: boolean;
      unit_type: string;
      unit_value: number;
      stock?: number;
      demo_pdf_url?: string;
      order_number?: number;
      [key: string]: any; // Add index signature for field validation
    }

    const product = await request.json() as ProductData;
    const env = locals.runtime.env;

    // Ensure the product exists
    const existingProduct = await getProductById(env, productId);

    if (!existingProduct) {
      return new Response(JSON.stringify({
        error: 'Product not found'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Validate required fields
    const requiredFields = ['name', 'price', 'description', 'category_id', 'unit_type', 'unit_value'];
    for (const field of requiredFields) {
      if (product[field] === undefined || product[field] === null) {
        return new Response(JSON.stringify({
          error: `Missing required field: ${field}`
        }), {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }
    }

    // Update the product in the database
    const result = await env.SNACKSWIFT_DB.prepare(`
      UPDATE products SET
        name = ?,
        url_slug = ?,
        description = ?,
        price = ?,
        old_price = ?,
        image = ?,
        category_id = ?,
        is_featured = ?,
        is_new = ?,
        is_on_sale = ?,
        is_available = ?,
        unit_type = ?,
        unit_value = ?,
        stock_quantity = ?,
        demo_pdf_url = ?,
        order_number = ?
      WHERE id = ?
    `).bind(
      product.name,
      product.url_slug,
      product.description,
      product.price,
      product.old_price || null,
      product.image || null,
      product.category_id,
      product.is_featured ? 1 : 0,
      product.is_new ? 1 : 0,
      product.is_on_sale ? 1 : 0,
      product.is_available ? 1 : 0,
      product.unit_type,
      product.unit_value,
      product.stock || 0,
      product.demo_pdf_url || null,
      product.order_number || 0,
      productId
    ).run();

    if (!result || !result.success) {
      throw new Error('Failed to update product');
    }

    // Retrieve the updated product
    const updatedProduct = await getProductById(env, productId);

    return new Response(JSON.stringify({
      success: true,
      product: updatedProduct,
      message: 'Product updated successfully'
    }), {
      headers: {
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Error updating product:', error);
    return new Response(JSON.stringify({
      error: 'Failed to update product',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Delete a product
export const DELETE: APIRoute = async ({ params, locals }) => {
  // Check admin authentication
  // In a production app, uncomment this code
  /*
  if (!locals.user || !locals.user.isAdmin) {
    return new Response(JSON.stringify({
      error: 'Unauthorized'
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  */

  try {
    const productId = params.id;

    if (!productId) {
      return new Response(JSON.stringify({
        error: 'Product ID is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    const env = locals.runtime.env;

    // Ensure the product exists before deleting
    const existingProduct = await getProductById(env, productId);

    if (!existingProduct) {
      return new Response(JSON.stringify({
        error: 'Product not found'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Delete the product
    const result = await env.SNACKSWIFT_DB.prepare(`
      DELETE FROM products WHERE id = ?
    `).bind(productId).run();

    if (!result || !result.success) {
      throw new Error('Failed to delete product');
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Product deleted successfully'
    }), {
      headers: {
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Error deleting product:', error);
    return new Response(JSON.stringify({
      error: 'Failed to delete product',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};