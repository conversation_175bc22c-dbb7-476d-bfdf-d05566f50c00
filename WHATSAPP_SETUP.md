# WhatsApp Business API Setup Guide

This guide will help you set up WhatsApp Business API integration for sending OTP messages.

## Prerequisites

1. **WhatsApp Business Account**: You need a verified WhatsApp Business account
2. **Meta Business Account**: Required for accessing WhatsApp Business API
3. **Phone Number**: A dedicated phone number for your business (cannot be used for personal WhatsApp)

## Step 1: Set up WhatsApp Business API

### Option A: Using Meta Business (Recommended for Production)

1. **Create a Meta Business Account**
   - Go to [business.facebook.com](https://business.facebook.com)
   - Create a new business account or use existing one

2. **Set up WhatsApp Business API**
   - Go to [developers.facebook.com](https://developers.facebook.com)
   - Create a new app or use existing one
   - Add "WhatsApp" product to your app

3. **Get Your Credentials**
   - **Phone Number ID**: Found in WhatsApp > API Setup (already configured: `***************`)
   - **Access Token**: Generate a permanent access token from your app settings

### Option B: Using WhatsApp Business API Test Environment (For Development)

1. **Access Test Environment**
   - Go to [developers.facebook.com](https://developers.facebook.com)
   - Create a test app
   - Add WhatsApp product

2. **Use Test Credentials**
   - Test Phone Number ID: `***************` (already configured)
   - Test Access Token: Available in the test environment

## Step 2: Configure Environment Variables

### For Local Development

Create a `.env` file in your project root:

```env
WHATSAPP_ACCESS_TOKEN=your_access_token_here
WHATSAPP_PHONE_NUMBER_ID=***************
```

### For Cloudflare Workers (Production)

1. **Set the Access Token as a Secret** (Recommended for sensitive data):
   ```bash
   npx wrangler secret put WHATSAPP_ACCESS_TOKEN
   ```
   Enter your access token when prompted.

2. **Alternative: Set as Environment Variable**:
   Update `wrangler.jsonc`:
   ```json
   "vars": { 
     "WHATSAPP_PHONE_NUMBER_ID": "***************",
     "WHATSAPP_ACCESS_TOKEN": "your_access_token_here"
   }
   ```

## Step 3: Set up Message Templates (Optional but Recommended)

For production use, WhatsApp requires pre-approved message templates for business communications.

### Create OTP Template

1. **Go to WhatsApp Manager**
   - Visit [business.facebook.com/wa/manage](https://business.facebook.com/wa/manage)
   - Select your WhatsApp Business Account

2. **Create Message Template**
   - Go to "Message Templates"
   - Click "Create Template"
   - Template Name: `otp_verification`
   - Category: `AUTHENTICATION`
   - Language: `English`
   - Template Content:
     ```
     Your Sreekar Publishers verification code is {{1}}. This code will expire in 10 minutes. Do not share this code with anyone.
     ```

3. **Submit for Approval**
   - Submit the template for Meta's review
   - Approval typically takes 24-48 hours

### Update Code to Use Template

Once your template is approved, the code will automatically use it. The current implementation includes fallback to text messages if templates are not available.

## Step 4: Test the Integration

### Test Phone Numbers

For testing, you can add test phone numbers in your WhatsApp Business API settings:

1. Go to your app in Meta for Developers
2. Navigate to WhatsApp > API Setup
3. Add test phone numbers under "To" field

### Test the OTP Flow

1. **Start your development server**:
   ```bash
   npm run dev
   ```

2. **Test the login flow**:
   - Go to `/login`
   - Enter a test phone number
   - Check if OTP is sent via WhatsApp

3. **Check logs**:
   - Success: Look for "WhatsApp OTP sent successfully" in console
   - Failure: Check error messages and fallback behavior

## Step 5: Production Considerations

### Security

1. **Use Secrets for Access Tokens**:
   ```bash
   npx wrangler secret put WHATSAPP_ACCESS_TOKEN
   ```

2. **Validate Webhook Signatures** (if using webhooks):
   - Implement webhook signature validation
   - Use app secret for verification

### Rate Limiting

- WhatsApp has rate limits for message sending
- Implement proper error handling and retry logic
- Consider implementing user-level rate limiting

### Message Templates

- Always use approved templates for production
- Have fallback mechanisms for template failures
- Monitor template approval status

### Monitoring

- Log all WhatsApp API calls and responses
- Monitor delivery rates and failures
- Set up alerts for API errors

## Troubleshooting

### Common Issues

1. **"Invalid phone number" error**:
   - Ensure phone number includes country code
   - Remove any special characters
   - Use format: `************` (for Indian numbers)

2. **"Template not found" error**:
   - Template not approved yet
   - Template name mismatch
   - Code falls back to text messages

3. **"Access token invalid" error**:
   - Token expired or revoked
   - Wrong token for the environment
   - Check token permissions

4. **"Phone number not registered" error**:
   - Phone number not added to test numbers
   - Number not verified with WhatsApp Business

### Debug Mode

The current implementation includes comprehensive logging:

- Success messages with Message IDs
- Error messages with details
- Fallback behavior when WhatsApp fails

### Support

- **WhatsApp Business API Documentation**: [developers.facebook.com/docs/whatsapp](https://developers.facebook.com/docs/whatsapp)
- **Meta Business Support**: Available through Meta Business Help Center
- **Community**: WhatsApp Business API Developer Community

## Current Implementation Features

✅ **Automatic fallback**: If WhatsApp fails, OTP is logged to console  
✅ **Template support**: Uses approved templates when available  
✅ **Text message fallback**: Falls back to text messages if templates fail  
✅ **Phone number validation**: Validates format before sending  
✅ **Error handling**: Comprehensive error handling and logging  
✅ **Development mode**: Shows OTP in response when WhatsApp is not configured  

## Next Steps

1. Get your WhatsApp Business API access token
2. Set up the environment variable
3. Test the integration
4. Create and approve message templates
5. Deploy to production

The integration is now ready to use! When properly configured, users will receive OTP codes via WhatsApp instead of SMS. 