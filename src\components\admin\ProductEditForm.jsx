import React, { useState, useEffect } from 'react';
import RichTextEditor from './RichTextEditor.jsx';
import '../../styles/quill.css';

// Helper function to validate Google Drive URLs
const isValidGoogleDriveUrl = (url) => {
  if (!url) return true; // Empty URL is valid (optional field)
  const googleDrivePattern = /^https:\/\/drive\.google\.com\/file\/d\/[a-zA-Z0-9_-]+\/(view|edit)(\?.*)?$/;
  return googleDrivePattern.test(url);
};

const ProductEditForm = ({ product }) => {
  const isEditMode = !!product;
  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    url_slug: '',
    description: '',
    price: '',
    old_price: '',
    image: '',
    category_id: '',
    is_featured: false,
    is_new: false,
    is_on_sale: false,
    is_available: true,
    unit_type: 'quantity',
    unit_value: 1,
    stock: 0,
    demo_pdf_url: '',
    order_number: 0
  });

  // Check URL parameters for markUnavailable flag
  useEffect(() => {
    // Check if we were redirected from product deletion with markUnavailable=true
    const urlParams = new URLSearchParams(window.location.search);
    const markUnavailable = urlParams.get('markUnavailable') === 'true';

    if (markUnavailable && product) {
      // Show a notification that the product can't be deleted
      setError(
        'This product cannot be deleted because it is referenced in orders. ' +
        'You can mark it as unavailable instead.'
      );

      // Auto-scroll to the top to show the message
      window.scrollTo({ top: 0, behavior: 'smooth' });

      // Clear the error after 8 seconds
      setTimeout(() => {
        setError(null);
      }, 8000);
    }
  }, [product]);

  // Load categories on mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/admin/categories');
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }

        const data = await response.json();
        setCategories(data.categories || []);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. Please try again.');
        // Fallback data for development
        setCategories([
          { id: '1', name: 'Main Course' },
          { id: '2', name: 'Appetizers' },
          { id: '3', name: 'Salads' },
          { id: '4', name: 'Desserts' },
          { id: '5', name: 'Beverages' }
        ]);
      }
    };

    fetchCategories();

    // Initialize form if product is provided (edit mode)
    if (product) {
      setFormData({
        name: product.name || '',
        url_slug: product.url_slug || '',
        description: product.description || '',
        price: product.price ? product.price.toString().replace(/[^\d.]/g, '') : '',
        old_price: product.old_price ? product.old_price.toString().replace(/[^\d.]/g, '') : '',
        image: product.image || '',
        category_id: product.category_id || '',
        is_featured: !!product.is_featured,
        is_new: !!product.is_new,
        is_on_sale: !!product.is_on_sale,
        is_available: product.is_available,
        unit_type: product.unit_type || 'quantity',
        unit_value: product.unit_value || 1,
        stock: product.stock_quantity || 0,
        demo_pdf_url: product.demo_pdf_url || '',
        order_number: product.order_number || 0
      });
    }
  }, [product]);

  // Simple handler for field blur
  const handleBlur = (e) => {
    // No validation needed
  };

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;

    // Use functional updates to avoid race conditions
    setFormData(prev => {
      const updatedData = { ...prev, [name]: newValue };

      // If name is changed, generate slug but don't trigger additional renders
      if (name === 'name') {
        const generatedSlug = value.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
        updatedData.url_slug = generatedSlug;
      }

      return updatedData;
    });
  };

  // Form submission handler
  const handleSubmit = async (e) => {
    e.preventDefault();

    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Determine if this is a create or update operation
      const isNew = !product?.id;
      const url = isNew
        ? '/api/admin/products'
        : `/api/admin/products/${product.id}`;

      const method = isNew ? 'POST' : 'PUT';

      const formDataToSubmit = {
        ...formData,
        price: Number(formData.price),
        old_price: formData.old_price ? Number(formData.old_price) : null,
        unit_value: Number(formData.unit_value),
        stock: Number(formData.stock)
      };

      // Add ID if in edit mode
      if (!isNew) {
        formDataToSubmit.id = product.id;
      }
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formDataToSubmit)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save product');
      }

      await response.json();
      setSuccess(true);

      // Redirect to products page after success
      setTimeout(() => {
        window.location.href = `/admin/products`;
      }, 1500);

    } catch (err) {
      console.error('Error saving product:', err);
      setError(err.message || 'An error occurred while saving the product.');
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Error/Success Messages */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <span className="material-icons-round text-red-500">error</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <span className="material-icons-round text-green-500">check_circle</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">
                Product {isEditMode ? 'updated' : 'created'} successfully!
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Basic Information Section */}
      <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Product Name */}
          <div className="col-span-2">
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Product Name*
            </label>
            <input
              id="name"
              name="name"
              type="text"
              required
              value={formData.name}
              onChange={handleChange}
              onBlur={handleBlur}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
          </div>

          {/* Order Number */}
          <div className="col-span-2 md:col-span-1">
            <label htmlFor="order_number" className="block text-sm font-medium text-gray-700 mb-1">
              Order Number
            </label>
            <input
              id="order_number"
              name="order_number"
              type="number"
              min="0"
              value={formData.order_number}
              onChange={handleChange}
              onBlur={handleBlur}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              Used to control the display order of products. Lower numbers appear first.
            </p>
          </div>

          {/* URL Slug */}
          <div className="col-span-2 md:col-span-1">
            <label htmlFor="url_slug" className="block text-sm font-medium text-gray-700 mb-1">
              URL Slug*
            </label>
            <input
              id="url_slug"
              name="url_slug"
              type="text"
              required
              value={formData.url_slug}
              onChange={handleChange}
              onBlur={handleBlur}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              Used in product URLs. Only lowercase letters, numbers, and hyphens.
            </p>
          </div>

          {/* Category */}
          <div className="md:col-span-1">
            <label htmlFor="category_id" className="block text-sm font-medium text-gray-700 mb-1">
              Category*
            </label>
            <select
              id="category_id"
              name="category_id"
              required
              value={formData.category_id}
              onChange={handleChange}
              onBlur={handleBlur}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="">Select Category</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>

          </div>

          {/* Description */}
          <div className="col-span-2">
            <RichTextEditor
              value={formData.description}
              onChange={(content) => setFormData(prev => ({ ...prev, description: content }))}
              placeholder="Enter a detailed description of the educational material..."
              required={true}
            />
          </div>
        </div>
      </div>

      {/* Pricing & Inventory Section */}
      <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Pricing & Inventory</h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Price */}
          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
              Price (₹)*
            </label>
            <input
              id="price"
              name="price"
              type="number"
              step="0.01"
              min="0"
              required
              value={formData.price}
              onChange={handleChange}
              onBlur={handleBlur}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />

          </div>

          {/* Original Price */}
          <div>
            <label htmlFor="old_price" className="block text-sm font-medium text-gray-700 mb-1">
              Original Price (₹)
              {formData.is_on_sale && <span className="text-red-600 ml-1">*</span>}
            </label>
            <input
              id="old_price"
              name="old_price"
              type="number"
              step="0.01"
              min="0"
              required={formData.is_on_sale}
              value={formData.old_price}
              onChange={handleChange}
              onBlur={handleBlur}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              {formData.is_on_sale ? 'Required when product is on sale' : 'Leave empty if not on sale'}
            </p>
          </div>

          {/* Stock */}
          <div>
            <label htmlFor="stock" className="block text-sm font-medium text-gray-700 mb-1">
              Stock*
            </label>
            <input
              id="stock"
              name="stock"
              type="number"
              min="0"
              required
              value={formData.stock}
              onChange={handleChange}
              onBlur={handleBlur}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />

          </div>

          {/* Unit Type */}
          <div>
            <label htmlFor="unit_type" className="block text-sm font-medium text-gray-700 mb-1">
              Unit Type*
            </label>
            <select
              id="unit_type"
              name="unit_type"
              required
              value={formData.unit_type}
              onChange={handleChange}
              onBlur={handleBlur}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="quantity">Quantity (items)</option>
              <option value="kg">Weight (kg)</option>
              <option value="g">Weight (g)</option>
              <option value="ml">Volume (ml)</option>
              <option value="l">Volume (l)</option>
            </select>
          </div>

          {/* Unit Value */}
          <div>
            <label htmlFor="unit_value" className="block text-sm font-medium text-gray-700 mb-1">
              Unit Value*
            </label>
            <input
              id="unit_value"
              name="unit_value"
              type="number"
              min="0"
              step={['kg', 'l'].includes(formData.unit_type) ? '0.01' : '1'}
              required
              value={formData.unit_value}
              onChange={handleChange}
              onBlur={handleBlur}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />

          </div>
        </div>
      </div>

      {/* Media Section */}
      <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Media</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Product Image */}
          <div>
            <label htmlFor="image" className="block text-sm font-medium text-gray-700 mb-1">
              Product Image URL
            </label>
            <input
              id="image"
              name="image"
              type="text"
              value={formData.image}
              onChange={handleChange}
              onBlur={handleBlur}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              Enter a URL for the product image
            </p>
          </div>

          {/* Image Preview */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Image Preview
            </label>
            <div className="w-full h-40 border border-gray-300 rounded-md bg-gray-100 flex items-center justify-center overflow-hidden">
              {formData.image ? (
                <img
                  src={formData.image}
                  alt="Product preview"
                  className="w-full h-full object-contain"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQVNer1ZryNxWVXojlY9Hoyy1-4DVNAmn7lrg&s';
                  }}
                />
              ) : (
                <span className="text-gray-400 material-icons-round text-4xl">image</span>
              )}
            </div>
          </div>

          {/* Demo PDF URL */}
          <div className="col-span-2">
            <label htmlFor="demo_pdf_url" className="block text-sm font-medium text-gray-700 mb-1">
              Demo PDF URL (Optional)
            </label>
            <input
              id="demo_pdf_url"
              name="demo_pdf_url"
              type="url"
              value={formData.demo_pdf_url}
              onChange={handleChange}
              onBlur={handleBlur}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="https://drive.google.com/file/d/your-file-id/view"
            />
            <p className="mt-1 text-xs text-gray-500">
              Enter a Google Drive sharing link for sample pages or preview content. This will be displayed as an embedded PDF on the product page.
            </p>
            {formData.demo_pdf_url && !isValidGoogleDriveUrl(formData.demo_pdf_url) && (
              <p className="mt-1 text-xs text-red-600">
                Please enter a valid Google Drive sharing URL (e.g., https://drive.google.com/file/d/your-file-id/view)
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Status Section */}
      <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Product Status</h2>

        <div className="flex flex-wrap gap-6">
          <div className="flex items-center">
            <input
              id="is_featured"
              name="is_featured"
              type="checkbox"
              checked={formData.is_featured}
              onChange={handleChange}
              className="h-5 w-5 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
            />
            <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-700">
              Featured Product
            </label>
          </div>

          <div className="flex items-center">
            <input
              id="is_new"
              name="is_new"
              type="checkbox"
              checked={formData.is_new}
              onChange={handleChange}
              className="h-5 w-5 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
            />
            <label htmlFor="is_new" className="ml-2 block text-sm text-gray-700">
              New Arrival
            </label>
          </div>

          <div className="flex items-center">
            <input
              id="is_on_sale"
              name="is_on_sale"
              type="checkbox"
              checked={formData.is_on_sale}
              onChange={handleChange}
              className="h-5 w-5 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
            />
            <label htmlFor="is_on_sale" className="ml-2 block text-sm text-gray-700">
              On Sale
            </label>
          </div>

          <div className="flex items-center">
            <input
              id="is_available"
              name="is_available"
              type="checkbox"
              checked={formData.is_available}
              onChange={handleChange}
              className="h-5 w-5 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
            />
            <label htmlFor="is_available" className="ml-2 block text-sm text-gray-700">
              Available for Purchase
            </label>
          </div>
        </div>

        {/* Sale Price Warning */}
        {formData.is_on_sale && (!formData.old_price || Number(formData.old_price) <= Number(formData.price)) && (
          <div className="mt-4 p-3 bg-yellow-50 border-l-4 border-yellow-400 text-yellow-800 text-sm">
            <div className="flex items-center">
              <span className="material-icons-round mr-2">warning</span>
              <span>
                When a product is on sale, the original price must be higher than the current price.
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3">
        <a
          href="/admin/products"
          className="px-6 py-2.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          Cancel
        </a>
        <button
          type="submit"
          disabled={isLoading}
          className="px-6 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:bg-orange-300 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <>
              <span className="inline-block animate-spin mr-1">⟳</span>
              {isEditMode ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            <>{isEditMode ? 'Update Product' : 'Create Product'}</>
          )}
        </button>
      </div>
    </form>
  );
};

export default ProductEditForm;