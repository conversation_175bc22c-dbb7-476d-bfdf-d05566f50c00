// Service Worker for Sreekar Publishers PWA
const CACHE_NAME = 'sreekar-publishers-v1';

// Critical assets that should be cached immediately during installation
const CRITICAL_ASSETS = [
  '/',
  '/manifest.json',
  '/images/icons/icon-192x192.png',
  '/images/icons/icon-512x512.png',
  '/styles/global.css'
];

// Additional assets that can be cached as they are requested
const ADDITIONAL_ASSETS = [
  '/images/sreekarpublishers-logo.jpeg',
  '/images/screenshots/home-screen.png',
  '/images/screenshots/product-screen.png',
  '/images/screenshots/cart-screen.png',
  '/scripts/swipe.js',
  '/scripts/cart-utils.js',
  '/scripts/favorites-utils.js'
];

self.addEventListener('install', event => {
  // Skip waiting to ensure the new service worker activates immediately
  self.skipWaiting();

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Caching critical assets');
        return cache.addAll(CRITICAL_ASSETS);
      })
  );
});

self.addEventListener('fetch', event => {
  // Always fetch from network for API requests or the homepage
  if (event.request.url.includes('/api/') || event.request.url === self.location.origin + '/') {
    return;
  }

  // Check if the request is for one of our additional assets
  const isAdditionalAsset = ADDITIONAL_ASSETS.some(asset =>
    event.request.url.endsWith(asset)
  );

  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Cache hit - return the response from the cached version
        if (response) {
          return response;
        }

        // Not in cache - fetch from network
        return fetch(event.request).then(
          response => {
            // Check if we received a valid response
            if(!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response
            const responseToCache = response.clone();

            // Add to cache - prioritize additional assets
            caches.open(CACHE_NAME)
              .then(cache => {
                // If this is one of our additional assets, cache it
                if (isAdditionalAsset ||
                    event.request.url.includes('/images/') ||
                    event.request.destination === 'image' ||
                    event.request.destination === 'style' ||
                    event.request.destination === 'font') {
                  console.log('Caching additional asset:', event.request.url);
                  cache.put(event.request, responseToCache);
                }
              });

            return response;
          }
        ).catch(error => {
          // If the network is unavailable, try to return any cached version
          console.log('Fetch failed, trying cache again:', error);
          return caches.match(event.request);
        });
      })
  );
});

self.addEventListener('activate', event => {
  const cacheWhitelist = [CACHE_NAME];

  // Claim clients immediately so the service worker starts controlling pages
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheWhitelist.indexOf(cacheName) === -1) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Claim clients
      self.clients.claim()
    ])
  );
});