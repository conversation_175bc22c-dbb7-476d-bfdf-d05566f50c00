import type { APIRoute } from 'astro';
import { authMiddleware } from '../../../middleware/auth';

interface Order {
    total_amount: number;
    delivery_fee: number | null;
}

interface RequestBody {
    orderId: number;
    deliveryFee: number;
}

export const POST: APIRoute = async ({ request, locals }) => {
    try {
        // Check admin authentication
        const authResult = await authMiddleware({ request, });
        if (authResult instanceof Response) {
            return authResult; // Return auth error response
        }

        if (!locals.runtime || !locals.runtime.env) {
            return new Response(
                JSON.stringify({ error: "Runtime environment not available" }),
                { status: 500, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const body = await request.json() as RequestBody;
        const { orderId, deliveryFee } = body;

        if (!orderId || deliveryFee === undefined) {
            return new Response(
                JSON.stringify({ error: "Missing required fields" }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // Validate delivery fee is a positive number
        const parsedDeliveryFee = parseFloat(deliveryFee.toString());
        if (isNaN(parsedDeliveryFee) || parsedDeliveryFee < 0) {
            return new Response(
                JSON.stringify({ error: "Invalid delivery fee amount" }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // Get the current order to calculate the new total
        const { results: currentOrder } = await locals.runtime.env.SNACKSWIFT_DB.prepare(
            "SELECT total_amount, delivery_fee FROM orders WHERE id = ?"
        ).bind(orderId).all();

        if (!currentOrder || currentOrder.length === 0) {
            return new Response(
                JSON.stringify({ error: "Order not found" }),
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const order = currentOrder[0] as unknown as Order;

        // Calculate the new total amount
        const newTotalAmount = order.total_amount - (order.delivery_fee || 0) + parsedDeliveryFee;

        // Update the order with new delivery fee and total amount
        const { success } = await locals.runtime.env.SNACKSWIFT_DB.prepare(
            "UPDATE orders SET delivery_fee = ?, total_amount = ? WHERE id = ?"
        ).bind(parsedDeliveryFee, newTotalAmount, orderId).run();

        if (!success) {
            throw new Error("Failed to update order");
        }

        return new Response(
            JSON.stringify({
                success: true,
                order: {
                    id: orderId,
                    delivery_fee: parsedDeliveryFee,
                    total_amount: newTotalAmount
                }
            }),
            { status: 200, headers: { 'Content-Type': 'application/json' } }
        );

    } catch (error) {
        console.error("Error updating delivery fee:", error);
        return new Response(
            JSON.stringify({ error: "Failed to update delivery fee" }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}; 