import type { APIRoute } from 'astro';
import { adminAuthMiddleware } from '../../../middleware/auth';
import { updateOrderStatus } from '../../../db/database';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const status = url.searchParams.get('status') || null;
    const search = url.searchParams.get('search') || null;

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build query based on filters
    let query = `
      SELECT o.*, 
             u.email as user_email, 
             u.name as user_name, 
             u.phone_number as user_phone,
             ol.address as location_address,
             ua.full_name as shipping_full_name,
             ua.phone as shipping_phone,
             ua.address as shipping_address,
             ua.city as shipping_city,
             ua.zip_code as shipping_zip_code,
             ua.instructions as shipping_instructions
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      LEFT JOIN order_locations ol ON o.location_id = ol.id
      LEFT JOIN user_addresses ua ON o.address_id = ua.id
      WHERE 1=1
    `;
    const queryParams: any[] = [];

    // Add status filter (supports single status or comma-separated multiple statuses)
    if (status && status !== 'all') {
      query += ` AND o.order_status NOT IN ('cancelled','delivered')`;
    }

    if (search) {
      query += ` AND (o.order_number LIKE ? OR u.email LIKE ? OR u.name LIKE ?)`;
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }

    // Count total orders (for pagination) - copy the same status logic
    let countQuery = `
      SELECT COUNT(*) as total
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      WHERE 1=1
    `;

    const countQueryParams: any[] = [];

    // Add same status filter logic for count query
    if (status && status !== 'all') {
      query += ` AND o.order_status NOT IN ('cancelled','delivered')`;

    }

    if (search) {
      countQuery += ` AND (o.order_number LIKE ? OR u.email LIKE ? OR u.name LIKE ?)`;
      const searchPattern = `%${search}%`;
      countQueryParams.push(searchPattern, searchPattern, searchPattern);
    }

    // Execute count query
    const countResult = await locals.runtime.env.SNACKSWIFT_DB.prepare(countQuery)
      .bind(...countQueryParams)
      .all();

    const totalOrders = Number(countResult.results?.[0]?.total || 0);

    // Add pagination to main query
    query += ` ORDER BY o.created_at DESC LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    // Execute main query
    const result = await locals.runtime.env.SNACKSWIFT_DB.prepare(query)
      .bind(...queryParams)
      .all();

    // Get status counts for filters
    const statusCounts = await getOrderStatusCounts(locals.runtime.env.SNACKSWIFT_DB);

    return new Response(
      JSON.stringify({
        success: true,
        orders: result.results || [],
        pagination: {
          page,
          limit,
          total: totalOrders,
          totalPages: Math.ceil(totalOrders / limit)
        },
        statusCounts
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

  } catch (error) {
    console.error('Error fetching admin orders:', error);

    return new Response(
      JSON.stringify({
        success: false,
        message: 'Failed to fetch orders'
      }),
      { status: 500 }
    );
  }
};

// Helper function to get counts by order status
async function getOrderStatusCounts(db: any) {
  try {
    const { results } = await db.prepare(`
      SELECT 
        order_status, 
        COUNT(*) as count
      FROM orders
      GROUP BY order_status
    `).all();

    // Also get total count
    const { results: totalResult } = await db.prepare(`
      SELECT COUNT(*) as count FROM orders
    `).all();

    const statusCounts: Record<string, any> = {
      total: totalResult?.[0]?.count || 0
    };

    // Add individual status counts
    if (results) {
      for (const row of results) {
        statusCounts[row.order_status] = row.count;
      }
    }

    return statusCounts;
  } catch (error) {
    console.error('Error getting order status counts:', error);
    return { total: 0 };
  }
}

/**
 * Bulk update orders (e.g., change status for multiple orders)
 */
export const PATCH: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get request data
    const requestData = await request.json() as { action: string; orderIds: number[]; status: string };
    const { action, orderIds, status } = requestData;

    // Validate the request
    if (action !== 'bulk_update_status') {
      return new Response(JSON.stringify({ error: "Invalid action" }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    if (!Array.isArray(orderIds) || orderIds.length === 0) {
      return new Response(JSON.stringify({ error: "No orders selected" }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    if (!status || typeof status !== "string") {
      return new Response(JSON.stringify({ error: "Valid status is required" }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Update each order status
    const results: {
      success: number[];
      failed: number[];
    } = {
      success: [],
      failed: []
    };

    for (const orderId of orderIds) {
      try {
        const success = await updateOrderStatus(
          locals.runtime.env,
          orderId,
          status
        );

        if (success) {
          results.success.push(orderId);
        } else {
          results.failed.push(orderId);
        }
      } catch (error) {
        console.error(`Error updating order ${orderId}:`, error);
        results.failed.push(orderId);
      }
    }

    // Return results
    return new Response(
      JSON.stringify({
        success: results.failed.length === 0,
        message: `Updated status to ${status} for ${results.success.length} orders. ${results.failed.length > 0 ? `Failed to update ${results.failed.length} orders.` : ''}`,
        results
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" }
      }
    );
  } catch (error) {
    console.error("Error performing bulk order update:", error);
    return new Response(JSON.stringify({ error: "Failed to update orders" }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
};