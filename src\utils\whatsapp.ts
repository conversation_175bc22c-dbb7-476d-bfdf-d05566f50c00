/**
 * WhatsApp Business API utilities for sending OTP messages
 */

interface WhatsAppMessage {
  messaging_product: string;
  to: string;
  type: string;
  template?: {
    name: string;
    language: {
      code: string;
    };
    components: Array<{
      type: string;
      parameters: Array<{
        type: string;
        text: string;
      }>;
    }>;
  };
  text?: {
    body: string;
  };
}

interface WhatsAppResponse {
  messaging_product: string;
  contacts: Array<{
    input: string;
    wa_id: string;
  }>;
  messages: Array<{
    id: string;
  }>;
}

/**
 * Send OTP via WhatsApp Business API
 */
export async function sendWhatsAppOTP(
  phoneNumber: string,
  otp: string,
  accessToken: string,
  phoneNumberId: string = '646817298521704'
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    // Format phone number (remove + and ensure it starts with country code)
    const formattedPhone = phoneNumber.replace(/^\+/, '').replace(/\D/g, '');
    
    // WhatsApp Business API endpoint
    const apiUrl = `https://graph.facebook.com/v21.0/${phoneNumberId}/messages`;
    
    // Message payload - using template for OTP (recommended for business use)
    const messagePayload: WhatsAppMessage = {
      messaging_product: "whatsapp",
      to: formattedPhone,
      type: "template",
      template: {
        name: "otp_verification", // You need to create this template in WhatsApp Business Manager
        language: {
          code: "en"
        },
        components: [
          {
            type: "body",
            parameters: [
              {
                type: "text",
                text: otp
              }
            ]
          }
        ]
      }
    };

    // If template is not available, fall back to text message
    // Note: Text messages have limitations and may require pre-approved templates
    const fallbackPayload: WhatsAppMessage = {
      messaging_product: "whatsapp",
      to: formattedPhone,
      type: "text",
      text: {
        body: `Your Sreekar Publishers verification code is: ${otp}\n\nThis code will expire in 10 minutes. Do not share this code with anyone.`
      }
    };

    // Try template first, then fallback to text
    let response: Response;
    let payload = messagePayload;

    try {
      response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      // If template fails (likely not approved), try text message
      if (!response.ok) {
        const errorData = await response.json() as any;
        console.log('Template message failed, trying text message:', errorData);
        
        payload = fallbackPayload;
        response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(payload)
        });
      }
    } catch (error) {
      console.error('Error with template, trying text message:', error);
      
      payload = fallbackPayload;
      response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
    }

    if (!response.ok) {
      const errorData = await response.json() as any;
      console.error('WhatsApp API error:', errorData);
      
      return {
        success: false,
        error: errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`
      };
    }

    const responseData: WhatsAppResponse = await response.json();
    
    return {
      success: true,
      messageId: responseData.messages[0]?.id
    };

  } catch (error) {
    console.error('Error sending WhatsApp OTP:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Validate phone number format for WhatsApp
 */
export function validateWhatsAppPhoneNumber(phoneNumber: string): boolean {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Should be at least 10 digits (without country code) or 12+ with country code
  if (cleaned.length < 10) {
    return false;
  }
  
  // If it starts with 91 (India), should be 12 digits total
  if (cleaned.startsWith('91') && cleaned.length === 12) {
    return true;
  }
  
  // If it's 10 digits, assume it's Indian number without country code
  if (cleaned.length === 10) {
    return true;
  }
  
  // For other country codes, accept 11-15 digits
  return cleaned.length >= 11 && cleaned.length <= 15;
}

/**
 * Format phone number for WhatsApp API
 */
export function formatPhoneNumberForWhatsApp(phoneNumber: string): string {
  // Remove all non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, '');
  
  // If it's 10 digits and doesn't start with country code, add India code
  if (cleaned.length === 10 && !cleaned.startsWith('91')) {
    cleaned = '91' + cleaned;
  }
  
  return cleaned;
} 