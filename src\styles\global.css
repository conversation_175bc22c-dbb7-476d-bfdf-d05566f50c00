@import "tailwindcss";

/* Global Styles */
/* @tailwind base;
@tailwind components;
@tailwind utilities; */

/* Professional Educational Design System */
:root {
  /* Educational Brand Colors - Professional and Trustworthy */
  --primary: #3b82f6;
  --primary-dark: #1e40af;
  --primary-light: #60a5fa;
  --secondary: #10b981;
  --secondary-dark: #047857;
  --accent: #f59e0b;
  --accent-dark: #d97706;

  /* Educational Semantic Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Professional Gray Scale */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Educational Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;

  /* Typography Scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Safe Area Insets */
  --safe-area-inset-bottom: env(safe-area-inset-bottom, 0);
  --safe-area-inset-top: env(safe-area-inset-top, 0);
  --safe-area-inset-left: env(safe-area-inset-left, 0px);
  --safe-area-inset-right: env(safe-area-inset-right, 0px);
}

/* Base styles that extend Tailwind */
html, body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  overscroll-behavior: none;
  /* Prevent text highlighting when tapping on elements */
  -webkit-user-select: none;
  user-select: none;
  /* Disable zoom on double tap */
  touch-action: manipulation;
  /* Prevent rubber-band scrolling on iOS */
  position: fixed;
  overflow: hidden;
  width: 100%;
  height: 100%;
  /* System font optimization */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

html {
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  background-color: var(--bg-secondary);
  -webkit-tap-highlight-color: transparent;
  overscroll-behavior-y: none;
  height: calc(100% + var(--safe-area-inset-bottom));
  scroll-behavior: smooth;
  font-size: var(--text-base);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  /* Professional educational background */
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);

  /* Scrolling behavior */
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior-y: none;

  /* Typography */
  color: var(--gray-700);
  font-size: var(--text-base);
  line-height: 1.6;
  font-weight: 400;

  /* Layout */
  min-height: 100vh;
  min-height: -webkit-fill-available;
  padding-bottom: calc(60px + var(--safe-area-inset-bottom));

  /* Performance optimizations */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;

  /* Enhanced visual hierarchy */
  letter-spacing: -0.01em;
}

/* Prevent blue highlight on tap */
* {
  -webkit-tap-highlight-color: transparent;
}

main {
  /* Ensure main content fills available space */
  min-height: calc(100vh - 56px - var(--safe-area-inset-bottom) - var(--safe-area-inset-top));
}

/* Utility classes beyond what Tailwind provides */
.no-tap-highlight {
  -webkit-tap-highlight-color: transparent;
}

/* Native-like button feedback */
.nav-tab, a, button {
  -webkit-tap-highlight-color: transparent;
  transition: transform 0.15s ease, opacity 0.15s ease;
}

/* Touch feedback similar to native apps */
.touch-active {
  opacity: 0.8;
  transition: opacity 0.15s;
}

/* Material-like ripple effect */
@keyframes ripple {
  from { opacity: 1; transform: scale(0); }
  to { opacity: 0; transform: scale(2.5); }
}

/* Navbar tab styles */
.nav-tab.active .nav-icon {
  transform: translateY(-3px);
  transition: transform 0.3s ease;
}

.nav-tab .nav-indicator {
  transition: opacity 0.3s ease;
}

.nav-tab {
  transition: all 0.3s ease;
}

.nav-tab .nav-icon-container {
  transition: background-color 0.25s ease, transform 0.2s ease;
  transform: scale(1);
}

.nav-tab:active .nav-icon-container {
  transform: scale(0.92);
}

.nav-tab.active .nav-icon-container {
  box-shadow: 0 2px 8px rgba(84, 102, 247, 0.25);
}

.nav-tab .material-icons-round {
  transition: color 0.25s ease;
}

.nav-tab span:not(.material-icons-round) {
  transition: color 0.25s ease, font-weight 0.25s ease;
}

.nav-tab.active .nav-icon-container {
  animation: pop 0.4s ease;
}

@keyframes pop {
  0% { transform: scale(0.9); }
  40% { transform: scale(1.1); }
  80% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

/* Native-like card styles */
.card {
  border-radius: 12px;
  background-color: white;
  overflow: hidden;
  will-change: transform;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.card:active {
  transform: scale(0.98);
}

/* Enhanced shadow for a more modern look */
.shadow-up {
  box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.05);
}

/* iOS-like back gesture area */
.back-gesture-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 100%;
  z-index: 30;
}

/* Animation keyframes */
@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Native app-like transition animations */
@keyframes slide-up {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-slide-up {
  animation: slide-up 0.3s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
}

.animate-fade-in {
  animation: fade-in 0.3s ease forwards;
}

.page-transition {
  animation: pageSlide 0.3s ease forwards;
}

@keyframes pageSlide {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Hide scrollbars but keep functionality */
.scrollbar-hide {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Progress bar styles */
.highq-progress-bar {
  position: fixed;
  top: 0;
  left: 0;
  height: 3px;
  background-color: #FF6B35;
  background-image: linear-gradient(
    to right,
    rgba(255, 107, 53, 0.8),
    rgba(255, 107, 53, 1),
    rgba(255, 107, 53, 0.8)
  );
  z-index: 9999;
  width: 0%;
  transition: width 0.3s cubic-bezier(0.65, 0, 0.35, 1), opacity 0.3s ease;
  pointer-events: none;
  box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
  animation: progress-pulse 1.5s ease-in-out infinite;
}

@keyframes progress-pulse {
  0% {
    box-shadow: 0 0 5px rgba(255, 107, 53, 0.5);
  }
  50% {
    box-shadow: 0 0 15px rgba(255, 107, 53, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(255, 107, 53, 0.5);
  }
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Image loading fade-in effect */
img {
  transition: opacity 0.3s ease;
}

img.lazy-load {
  opacity: 0;
}

img.lazy-loaded {
  opacity: 1;
}

/* Improved notification styling */
.notification-container {
  pointer-events: none;
}

.notification-toast {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  max-width: calc(100% - 32px);
}

/* Common UI elements */
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 100px;
}

.chip {
  display: inline-flex;
  align-items: center;
  padding: 0.35rem 0.75rem;
  border-radius: 100px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #f3f4f6;
  color: #374151;
}

.divider {
  height: 1px;
  width: 100%;
  background-color: #e5e7eb;
  margin: 1rem 0;
}

/* Card hover effects */
.product-card .product-image-container {
  overflow: hidden;
}

.product-card img {
  transition: transform 0.5s ease;
}

.product-card:hover img {
  transform: scale(1.05);
}

.product-card .quick-add-btn {
  transition: all 0.2s ease;
}

.product-card:hover .quick-add-btn {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-hover {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

/* Modal animations */
.modal-enter {
  animation: modalEnter 0.3s forwards;
}

@keyframes modalEnter {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Professional homepage styles */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced card hover effects for educational materials */
.educational-card {
  background: var(--bg-primary);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.educational-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-light);
}

.educational-card:active {
  transform: translateY(-2px);
}

/* Professional gradient backgrounds for educational theme */
.hero-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

.educational-gradient {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary) 100%);
}

.accent-gradient {
  background: linear-gradient(135deg, var(--accent) 0%, var(--accent-dark) 100%);
}

/* Smooth animations for sections */
.section-fade-in {
  animation: sectionFadeIn 0.6s ease-out forwards;
}

@keyframes sectionFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Professional Educational Button System */
.btn-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--text-sm);
  letter-spacing: 0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(59, 130, 246, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background: var(--bg-primary);
  color: var(--primary);
  border: 2px solid var(--primary);
  padding: calc(var(--space-3) - 2px) calc(var(--space-6) - 2px);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--text-sm);
  letter-spacing: 0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
}

.btn-secondary:active {
  transform: translateY(0);
}

.btn-outline {
  background: transparent;
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: var(--text-sm);
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-outline:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  color: var(--gray-800);
}

.btn-success {
  background: linear-gradient(135deg, var(--secondary) 0%, var(--secondary-dark) 100%);
  color: white;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--text-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(16, 185, 129, 0.4);
}

/* Educational icons animation */
.educational-icon {
  transition: transform 0.3s ease;
}

.educational-icon:hover {
  transform: scale(1.1) rotate(5deg);
}

/* Professional Educational Search Bar */
.search-input {
  background: var(--bg-primary);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-xl);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  color: var(--gray-700);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
  width: 100%;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-md);
  transform: translateY(-1px);
}

.search-input::placeholder {
  color: var(--gray-400);
  font-weight: 400;
}

/* Enhanced form controls */
.form-input {
  background: var(--bg-primary);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
  color: var(--gray-700);
  transition: all 0.2s ease;
  width: 100%;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-label {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
  display: block;
}

/* Professional card system */
.card-elevated {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-100);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

/* Professional badge system */
.badge-primary {
  background: var(--primary);
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 600;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.badge-secondary {
  background: var(--secondary);
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 600;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.badge-outline {
  background: transparent;
  color: var(--gray-600);
  border: 1px solid var(--gray-300);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 500;
}