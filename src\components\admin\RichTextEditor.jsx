import React, { useState, useEffect, useRef } from 'react';

// Dynamically import DOMPurify for client-side use
let DOMPurify = null;
if (typeof window !== 'undefined') {
  import('dompurify').then(module => {
    DOMPurify = module.default;
  });
}

const RichTextEditor = ({
  value = '',
  onChange,
  placeholder = 'Enter description...',
  required = false,
  error = null
}) => {
  const [editorValue, setEditorValue] = useState(value);
  const [showPreview, setShowPreview] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [showTableMenu, setShowTableMenu] = useState(false);
  const [cursorInTable, setCursorInTable] = useState(false);
  const editorRef = useRef(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Initialize editor content when component mounts or when it becomes client-side
  useEffect(() => {
    if (editorRef.current && isClient && !editorRef.current.hasAttribute('data-initialized')) {
      editorRef.current.innerHTML = editorValue || '';
      editorRef.current.setAttribute('data-initialized', 'true');
    }
  }, [isClient]);

  // Only update content when value prop changes from external source and editor is not focused
  useEffect(() => {
    if (editorRef.current && isClient && document.activeElement !== editorRef.current) {
      // Only update if the content is actually different
      if (editorRef.current.innerHTML !== editorValue && editorValue !== value) {
        editorRef.current.innerHTML = editorValue || '';
      }
    }
  }, [editorValue, isClient]);

  // Update internal state when value prop changes (only for external updates)
  useEffect(() => {
    // Only update if the value is different and the editor is not focused
    if (value !== editorValue && document.activeElement !== editorRef.current) {
      setEditorValue(value);
      if (editorRef.current && isClient) {
        editorRef.current.innerHTML = value || '';
      }
    }
  }, [value, isClient]);

  // Handle content changes
  const handleContentChange = () => {
    if (editorRef.current && onChange) {
      const content = editorRef.current.innerHTML;
      const cleanContent = content === '<br>' || content === '<div><br></div>' ? '' : content;
      
      // Only update state if content actually changed
      if (cleanContent !== editorValue) {
        setEditorValue(cleanContent);
        onChange(cleanContent);
      }
    }
    
    // Update cursor position state
    setCursorInTable(isInsideTable());
  };

  // Handle cursor/selection changes
  const handleSelectionChange = () => {
    setCursorInTable(isInsideTable());
  };

  // Add event listener for selection changes
  useEffect(() => {
    if (isClient) {
      document.addEventListener('selectionchange', handleSelectionChange);
      return () => {
        document.removeEventListener('selectionchange', handleSelectionChange);
      };
    }
  }, [isClient]);

  // Format commands
  const execCommand = (command, value = null) => {
    document.execCommand(command, false, value);
    editorRef.current?.focus();
    handleContentChange();
  };

  // Insert table
  const insertTable = () => {
    const tableHTML = `
      <table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">
        <thead>
          <tr>
            <th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;">Header 1</th>
            <th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;">Header 2</th>
            <th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;">Header 3</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">Cell 1</td>
            <td style="border: 1px solid #ddd; padding: 8px;">Cell 2</td>
            <td style="border: 1px solid #ddd; padding: 8px;">Cell 3</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 8px;">Cell 4</td>
            <td style="border: 1px solid #ddd; padding: 8px;">Cell 5</td>
            <td style="border: 1px solid #ddd; padding: 8px;">Cell 6</td>
          </tr>
        </tbody>
      </table>
    `;
    
    if (editorRef.current) {
      editorRef.current.focus();
      document.execCommand('insertHTML', false, tableHTML);
      handleContentChange();
    }
  };

  // Get current table cell
  const getCurrentTableCell = () => {
    const selection = window.getSelection();
    if (selection.rangeCount === 0) return null;
    
    let element = selection.getRangeAt(0).commonAncestorContainer;
    
    // If it's a text node, get its parent
    if (element.nodeType === Node.TEXT_NODE) {
      element = element.parentElement;
    }
    
    // Find the closest td or th
    while (element && element !== editorRef.current) {
      if (element.tagName === 'TD' || element.tagName === 'TH') {
        return element;
      }
      element = element.parentElement;
    }
    
    return null;
  };

  // Add row after current row
  const addRowAfter = () => {
    const cell = getCurrentTableCell();
    if (!cell) return;
    
    const row = cell.parentElement;
    const table = row.closest('table');
    const colCount = row.cells.length;
    
    const newRow = document.createElement('tr');
    for (let i = 0; i < colCount; i++) {
      const newCell = document.createElement('td');
      newCell.style.cssText = 'border: 1px solid #ddd; padding: 8px;';
      newCell.textContent = `New Cell ${i + 1}`;
      newRow.appendChild(newCell);
    }
    
    row.parentElement.insertBefore(newRow, row.nextSibling);
    handleContentChange();
  };

  // Add row before current row
  const addRowBefore = () => {
    const cell = getCurrentTableCell();
    if (!cell) return;
    
    const row = cell.parentElement;
    const colCount = row.cells.length;
    
    const newRow = document.createElement('tr');
    for (let i = 0; i < colCount; i++) {
      const newCell = document.createElement('td');
      newCell.style.cssText = 'border: 1px solid #ddd; padding: 8px;';
      newCell.textContent = `New Cell ${i + 1}`;
      newRow.appendChild(newCell);
    }
    
    row.parentElement.insertBefore(newRow, row);
    handleContentChange();
  };

  // Delete current row
  const deleteRow = () => {
    const cell = getCurrentTableCell();
    if (!cell) return;
    
    const row = cell.parentElement;
    const table = row.closest('table');
    
    // Don't delete if it's the only row in tbody or thead
    const tbody = row.parentElement;
    if (tbody.children.length <= 1) return;
    
    row.remove();
    handleContentChange();
  };

  // Add column after current column
  const addColumnAfter = () => {
    const cell = getCurrentTableCell();
    if (!cell) return;
    
    const cellIndex = Array.from(cell.parentElement.children).indexOf(cell);
    const table = cell.closest('table');
    
    // Add header cell if there's a thead
    const thead = table.querySelector('thead');
    if (thead) {
      const headerRow = thead.querySelector('tr');
      const newHeaderCell = document.createElement('th');
      newHeaderCell.style.cssText = 'border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; font-weight: bold;';
      newHeaderCell.textContent = 'New Header';
      
      if (cellIndex + 1 < headerRow.children.length) {
        headerRow.insertBefore(newHeaderCell, headerRow.children[cellIndex + 1]);
      } else {
        headerRow.appendChild(newHeaderCell);
      }
    }
    
    // Add cells to all body rows
    const tbody = table.querySelector('tbody');
    if (tbody) {
      const rows = tbody.querySelectorAll('tr');
      rows.forEach(row => {
        const newCell = document.createElement('td');
        newCell.style.cssText = 'border: 1px solid #ddd; padding: 8px;';
        newCell.textContent = 'New Cell';
        
        if (cellIndex + 1 < row.children.length) {
          row.insertBefore(newCell, row.children[cellIndex + 1]);
        } else {
          row.appendChild(newCell);
        }
      });
    }
    
    handleContentChange();
  };

  // Add column before current column
  const addColumnBefore = () => {
    const cell = getCurrentTableCell();
    if (!cell) return;
    
    const cellIndex = Array.from(cell.parentElement.children).indexOf(cell);
    const table = cell.closest('table');
    
    // Add header cell if there's a thead
    const thead = table.querySelector('thead');
    if (thead) {
      const headerRow = thead.querySelector('tr');
      const newHeaderCell = document.createElement('th');
      newHeaderCell.style.cssText = 'border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; font-weight: bold;';
      newHeaderCell.textContent = 'New Header';
      headerRow.insertBefore(newHeaderCell, headerRow.children[cellIndex]);
    }
    
    // Add cells to all body rows
    const tbody = table.querySelector('tbody');
    if (tbody) {
      const rows = tbody.querySelectorAll('tr');
      rows.forEach(row => {
        const newCell = document.createElement('td');
        newCell.style.cssText = 'border: 1px solid #ddd; padding: 8px;';
        newCell.textContent = 'New Cell';
        row.insertBefore(newCell, row.children[cellIndex]);
      });
    }
    
    handleContentChange();
  };

  // Delete current column
  const deleteColumn = () => {
    const cell = getCurrentTableCell();
    if (!cell) return;
    
    const cellIndex = Array.from(cell.parentElement.children).indexOf(cell);
    const table = cell.closest('table');
    
    // Don't delete if it's the only column
    if (cell.parentElement.children.length <= 1) return;
    
    // Remove header cell if there's a thead
    const thead = table.querySelector('thead');
    if (thead) {
      const headerRow = thead.querySelector('tr');
      if (headerRow.children[cellIndex]) {
        headerRow.children[cellIndex].remove();
      }
    }
    
    // Remove cells from all body rows
    const tbody = table.querySelector('tbody');
    if (tbody) {
      const rows = tbody.querySelectorAll('tr');
      rows.forEach(row => {
        if (row.children[cellIndex]) {
          row.children[cellIndex].remove();
        }
      });
    }
    
    handleContentChange();
  };

  // Check if cursor is inside a table
  const isInsideTable = () => {
    return getCurrentTableCell() !== null;
  };

  // Check if command is active
  const isCommandActive = (command) => {
    return document.queryCommandState(command);
  };

  // Sanitize HTML for preview
  const sanitizeHtml = (html) => {
    if (typeof window === 'undefined') return html;

    // Use DOMPurify if available, otherwise basic sanitization
    if (DOMPurify) {
      return DOMPurify.sanitize(html, {
        ALLOWED_TAGS: ['p', 'br', 'strong', 'b', 'em', 'i', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 
                       'ul', 'ol', 'li', 'a', 'table', 'thead', 'tbody', 'tr', 'th', 'td', 'div'],
        ALLOWED_ATTR: ['href', 'target', 'rel', 'border', 'style', 'class']
      });
    }

    // Fallback basic sanitization
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Remove script tags and other dangerous elements
    const scripts = tempDiv.querySelectorAll('script, iframe, object, embed');
    scripts.forEach(script => script.remove());

    return tempDiv.innerHTML;
  };

  // Toggle between editor and preview mode
  const togglePreview = () => {
    if (!showPreview && editorRef.current) {
      // Save current content before switching to preview
      const content = editorRef.current.innerHTML;
      const cleanContent = content === '<br>' || content === '<div><br></div>' ? '' : content;
      setEditorValue(cleanContent);
      if (onChange) {
        onChange(cleanContent);
      }
    }
    setShowPreview(!showPreview);
  };
  
  // Handle click within the editor to ensure proper cursor placement
  const handleEditorClick = (event) => {
    // Close table menu when clicking in editor
    setShowTableMenu(false);
    
    // Update cursor position after a short delay to ensure the click has been processed
    setTimeout(() => {
      setCursorInTable(isInsideTable());
    }, 10);
  };

  // Close table menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.table-menu-container')) {
        setShowTableMenu(false);
      }
    };

    if (showTableMenu) {
      document.addEventListener('click', handleClickOutside);
      return () => {
        document.removeEventListener('click', handleClickOutside);
      };
    }
  }, [showTableMenu]);

  // Toolbar button component
  const ToolbarButton = ({ command, icon, title, value = null, isActive = false }) => (
    <button
      type="button"
      onClick={() => execCommand(command, value)}
      className={`p-2 rounded border transition-colors ${
        isActive
          ? 'bg-orange-500 text-white border-orange-500'
          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
      }`}
      title={title}
    >
      <span className="material-icons-round text-sm">{icon}</span>
    </button>
  );

  if (!isClient) {
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Description{required && <span className="text-red-600 ml-1">*</span>}
        </label>
        <div className="w-full h-40 border border-gray-300 rounded-md bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500 mx-auto mb-2"></div>
            <p className="text-sm text-gray-500">Loading editor...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          Description{required && <span className="text-red-600 ml-1">*</span>}
        </label>
        {/* <button
          type="button"
          onClick={togglePreview}
          className="text-sm text-orange-600 hover:text-orange-800 font-medium flex items-center"
        >
          <span className="material-icons-round text-sm mr-1">
            {showPreview ? 'edit' : 'visibility'}
          </span>
          {showPreview ? 'Edit' : 'Preview'}
        </button> */}
      </div>

      {!showPreview ? (
        <div className="border border-gray-300 rounded-md overflow-hidden">
          {/* Add CSS for placeholder and table styling */}
          <style dangerouslySetInnerHTML={{
            __html: `
              .rich-text-editor[contenteditable]:empty:before {
                content: attr(data-placeholder);
                color: #9CA3AF;
                pointer-events: none;
              }
              .rich-text-editor {
                text-align: left;
                direction: ltr;
                unicode-bidi: embed;
              }
              .rich-text-editor * {
                direction: ltr;
                unicode-bidi: embed;
              }
              .rich-text-editor table {
                border-collapse: collapse;
                width: 100%;
                margin: 10px 0;
                border: 1px solid #ddd;
              }
              .rich-text-editor table th,
              .rich-text-editor table td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
              }
              .rich-text-editor table th {
                background-color: #f5f5f5;
                font-weight: bold;
              }
              .rich-text-editor table tr:nth-child(even) {
                background-color: #f9f9f9;
              }
              .rich-text-editor table tr:hover {
                background-color: #f5f5f5;
              }
            `
          }} />
          
          {/* Custom Toolbar */}
          <div className="bg-gray-50 border-b border-gray-300 p-2 flex flex-wrap gap-1">
            {/* Text formatting */}
            <ToolbarButton
              command="bold"
              icon="format_bold"
              title="Bold"
              isActive={isCommandActive('bold')}
            />
            <ToolbarButton
              command="italic"
              icon="format_italic"
              title="Italic"
              isActive={isCommandActive('italic')}
            />
            <ToolbarButton
              command="underline"
              icon="format_underlined"
              title="Underline"
              isActive={isCommandActive('underline')}
            />
            
            <div className="w-px bg-gray-300 mx-1"></div>
            
            {/* Lists */}
            <ToolbarButton
              command="insertUnorderedList"
              icon="format_list_bulleted"
              title="Bullet List"
            />
            <ToolbarButton
              command="insertOrderedList"
              icon="format_list_numbered"
              title="Numbered List"
            />
            
            <div className="w-px bg-gray-300 mx-1"></div>
            
            {/* Alignment */}
            <ToolbarButton
              command="justifyLeft"
              icon="format_align_left"
              title="Align Left"
            />
            <ToolbarButton
              command="justifyCenter"
              icon="format_align_center"
              title="Align Center"
            />
            <ToolbarButton
              command="justifyRight"
              icon="format_align_right"
              title="Align Right"
            />
            
            <div className="w-px bg-gray-300 mx-1"></div>
            
            {/* Heading and Table */}
            <ToolbarButton
              command="formatBlock"
              icon="title"
              title="Heading"
              value="h3"
            />
            
            {/* Table button with custom handler */}
            <button
              type="button"
              onClick={insertTable}
              className="p-2 rounded border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              title="Insert Table"
            >
              <span className="material-icons-round text-sm">table_chart</span>
            </button>

            {/* Table management options - show only when cursor is in table */}
            {cursorInTable && (
              <>
                <div className="w-px bg-gray-300 mx-1"></div>
                
                {/* Row operations */}
                <div className="relative table-menu-container">
                  <button
                    type="button"
                    onClick={() => setShowTableMenu(!showTableMenu)}
                    className="p-2 rounded border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                    title="Table Options"
                  >
                    <span className="material-icons-round text-sm">more_vert</span>
                  </button>
                  
                  {showTableMenu && (
                    <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10 min-w-[180px]">
                      <div className="py-1">
                        <button
                          type="button"
                          onClick={() => { addRowBefore(); setShowTableMenu(false); }}
                          className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        >
                          <span className="material-icons-round text-sm mr-2">add</span>
                          Add Row Above
                        </button>
                        <button
                          type="button"
                          onClick={() => { addRowAfter(); setShowTableMenu(false); }}
                          className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        >
                          <span className="material-icons-round text-sm mr-2">add</span>
                          Add Row Below
                        </button>
                        <button
                          type="button"
                          onClick={() => { deleteRow(); setShowTableMenu(false); }}
                          className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
                        >
                          <span className="material-icons-round text-sm mr-2">remove</span>
                          Delete Row
                        </button>
                        
                        <div className="border-t border-gray-200 my-1"></div>
                        
                        <button
                          type="button"
                          onClick={() => { addColumnBefore(); setShowTableMenu(false); }}
                          className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        >
                          <span className="material-icons-round text-sm mr-2">add</span>
                          Add Column Left
                        </button>
                        <button
                          type="button"
                          onClick={() => { addColumnAfter(); setShowTableMenu(false); }}
                          className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        >
                          <span className="material-icons-round text-sm mr-2">add</span>
                          Add Column Right
                        </button>
                        <button
                          type="button"
                          onClick={() => { deleteColumn(); setShowTableMenu(false); }}
                          className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
                        >
                          <span className="material-icons-round text-sm mr-2">remove</span>
                          Delete Column
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>

          {/* Content Editable Area */}
          <div
            ref={editorRef}
            contentEditable
            className="rich-text-editor p-4 min-h-[150px] focus:outline-none prose prose-sm max-w-none"
            style={{
              backgroundColor: 'white',
              lineHeight: '1.6',
              direction: 'ltr',
              unicodeBidi: 'embed',
              textAlign: 'left'
            }}
            onInput={handleContentChange}
            onBlur={handleContentChange}
            onClick={handleEditorClick}
            onKeyDown={(e) => {
              // Handle special keys that might change content structure
              if (e.key === 'Enter' || e.key === 'Backspace' || e.key === 'Delete') {
                setTimeout(() => handleContentChange(), 10);
              }
            }}
            data-placeholder={placeholder}
            dir="ltr"
            spellCheck="true"
            lang="en"
          />
        </div>
      ) : (
        <div className="border border-gray-300 rounded-md p-4 bg-gray-50 min-h-[150px]">
          <div className="text-sm text-gray-600 mb-2 font-medium">Preview:</div>
          <div
            className="prose prose-sm max-w-none"
            dangerouslySetInnerHTML={{
              __html: sanitizeHtml(editorValue) || '<p class="text-gray-400 italic">No content to preview</p>'
            }}
          />
        </div>
      )}

      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}

      <p className="text-xs text-gray-500">
        Use the toolbar to format your text. You can add headings, bold/italic text, lists, tables, and links.
        {cursorInTable && <span className="text-orange-600"> • Click the menu button (⋮) to manage table rows and columns.</span>}
      </p>
    </div>
  );
};

export default RichTextEditor;
